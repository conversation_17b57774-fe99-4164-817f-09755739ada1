{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Django: Debug Server",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/backend/manage.py",
            "args": [
                "runserver",
                "0.0.0.0:12345",
                "--noreload"
            ],
            "django": true,
            "autoStartBrowser": false,
            "cwd": "${workspaceFolder}/backend",
            "env": {
                "DJANGO_DEBUG": "True"
            },
            "console": "integratedTerminal",
            "justMyCode": false,
            "subProcess": false
        },
        {
            "name": "Django: Debug Server (Auto-reload)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/backend/manage.py",
            "args": [
                "runserver",
                "0.0.0.0:12345"
            ],
            "django": true,
            "autoStartBrowser": false,
            "cwd": "${workspaceFolder}/backend",
            "env": {
                "DJANGO_DEBUG": "True"
            },
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Django: Run Tests",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/backend/manage.py",
            "args": [
                "test"
            ],
            "django": true,
            "cwd": "${workspaceFolder}/backend",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Django: Shell",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/backend/manage.py",
            "args": [
                "shell"
            ],
            "django": true,
            "cwd": "${workspaceFolder}/backend",
            "console": "integratedTerminal",
            "justMyCode": false
        }
    ]
}