"""
URL configuration for dj_my_steward project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.urls import path, re_path, include
from django.views.static import serve

from main.settings import MEDIA_ROOT

urlpatterns = [
    path("admin/", admin.site.urls),

    # 媒体站
    path('', include('media_station.urls')),

    # api
    path('api/', include('api.urls')),

    # 静态资源访问支持
    re_path(r'media/(?P<path>.*)$', serve, {"document_root": MEDIA_ROOT}),
]

urlpatterns += staticfiles_urlpatterns()  # 开发阶段访问 static 支持
