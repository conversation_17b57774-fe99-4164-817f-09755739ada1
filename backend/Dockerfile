# 后端 Dockerfile
# Django 应用的生产环境配置

FROM python:3.8-alpine

# 环境变量设置
# 设置 Python 环境变量
# 禁用 Python 输出缓冲，实时显示日志
ENV PYTHONUNBUFFERED=1
# 不生成 .pyc 文件，减少镜像大小
ENV PYTHONDONTWRITEBYTECODE=1

# 设置工作目录
WORKDIR /app

# 更新包索引并安装系统依赖
RUN apk update && apk add --no-cache \
    gcc \
    musl-dev \
    python3-dev \
    libffi-dev \
    openssl-dev \
    curl

# 安装 uv（使用curl安装）
RUN curl -LsSf https://astral.sh/uv/install.sh | sh

# 将uv添加到PATH
ENV PATH="/root/.local/bin:$PATH"

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# uv 同步安装依赖到虚拟环境 .venv，--frozen 确保使用 uv.lock 中的精确版本
RUN uv sync --frozen

# 复制源代码
COPY . .

# 数据库/media 外部路径
COPY .env.docker .env

# 创建日志目录
RUN mkdir -p /app/logs

# 收集静态文件，Django 的静态文件需要收集到统一目录
RUN uv run python manage.py collectstatic --noinput

# 暴露端口
EXPOSE 12345

# 启动命令
# 使用 gunicorn 作为 WSGI 服务器，比 Django 开发服务器更适合生产环境
CMD ["uv", "run", "gunicorn", "--config", "gunicorn_config.py", "main.wsgi:application"] 