document.addEventListener("DOMContentLoaded", function () {
  const sortBtn = document.querySelector(".sort-btn");
  const addSubjectBtn = document.querySelector(".add-subject-btn"); // 添加-专题 按钮
  const sortActions = document.querySelector(".sort-actions");
  const sortConfirm = document.querySelector(".sort-confirm");
  const sortCancel = document.querySelector(".sort-cancel");
  const subjectList = document.querySelector(".subject-list");
  let sortable = null;

  // 初始化Sortable
  function initSortable() {
    return Sortable.create(subjectList, {
      animation: 150,
      handle: ".subject-item",
      onStart: function () {
        // 开始拖动时的处理
        subjectList.classList.add("sorting");
      },
      onEnd: function () {
        // 结束拖动时的处理
        subjectList.classList.remove("sorting");
      },
    });
  }
  // 启用排序模式
  function enableSortMode() {
    sortBtn.style.display = "none"; // 隐藏

    sortActions.classList.add("active"); // 变为 .sort-actions.active，显示 确认、取消 按钮

    // 禁用所有链接点击
    subjectList.querySelectorAll("a").forEach((a) => {
      a.style.pointerEvents = "none";
    });
    // 初始化拖拽
    sortable = initSortable();
    // 添加排序模式样式
    subjectList.classList.add("sort-mode");
  }

  // 禁用排序模式
  function disableSortMode() {
    sortBtn.style.display = "flex";
    sortActions.style.display = "none";
    // 恢复链接点击
    subjectList.querySelectorAll("a").forEach((a) => {
      a.style.pointerEvents = "auto";
    });
    // 销毁拖拽实例
    if (sortable) {
      sortable.destroy();
      sortable = null;
    }
    // 移除排序模式样式
    subjectList.classList.remove("sort-mode");
  }

  // 获取排序后的ID列表
  function getSortedIds() {
    return Array.from(subjectList.children)
      .filter((el) => el.dataset.id)
      .map((el) => el.dataset.id);
  }

  // 获取CSRF Token
  function getCSRFToken() {
    const name = "csrftoken";
    let cookieValue = null;
    if (document.cookie && document.cookie !== "") {
      const cookies = document.cookie.split(";");
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        if (cookie.substring(0, name.length + 1) === name + "=") {
          cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
          break;
        }
      }
    }
    return cookieValue;
  }

  // 提交排序结果到后端
  async function submitSort(sortedIds) {
    try {
      const response = await fetch("/v3/api/subject/all_sort/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCSRFToken(),
        },
        body: JSON.stringify({
          li_subject_id: sortedIds,
        }),
      });

      const jo = await response.json();
      if (jo.code !== 0) {
        showToast(jo.msg || "排序失败", "error");
      }

      // 成功，页面刷新
      location.reload();
    } catch (error) {
      console.error("排序出错：", error);
      alert("排序保存失败，请重试！");
    }
  }

  // 点击排序按钮
  if (sortBtn) {
    sortBtn.addEventListener("click", function () {
      enableSortMode();
    });
  }

  // 点击确认按钮
  if (sortConfirm) {
    sortConfirm.addEventListener("click", async function () {
      const sortedIds = getSortedIds();
      console.log("排序确认，ID顺序：", sortedIds);

      // 禁用确认按钮，防止重复提交
      sortConfirm.disabled = true;

      // 调用API保存排序
      await submitSort(sortedIds);

      // 恢复确认按钮状态
      sortConfirm.disabled = false;

      disableSortMode();
    });
  }

  // 点击取消按钮
  if (sortCancel) {
    sortCancel.addEventListener("click", function () {
      location.reload();
    });
  }

  // 点击添加专题按钮（弹窗形式）
  addSubjectBtn.addEventListener("click", function () {
    // 创建模态框
    const modal = createEditModal((width = "400px"));
    modal.show({
      title: "添加专题",
      content: `
        <input type="text" class="modal-input" style="margin: 20px 0" placeholder="请输入专题名称" />
      `,
      footerHtml: `
        <div class="my-flex-end" style="width: 100%;">
          <button class="modal-confirm btn-primary" id="addSubjectConfirmBtn">确定</button>
        </div>
      `,
    });

    // 绑定 确定按钮 添加专题
    modal.element
      .querySelector("#addSubjectConfirmBtn")
      .addEventListener("click", async () => {
        const title = modal.element.querySelector("input").value;
        console.log("添加专题：", title);

        const fd = new FormData();
        fd.append("title", title);

        const response = await fetch("/v3/api/subject/add/", {
          method: "POST",
          headers: {
            "X-CSRFToken": getCSRFToken(),
          },
          body: fd,
        });

        const jo = await response.json();
        if (jo.code !== 0) {
          showToast(jo.msg || "添加失败", "error");
        } else {
          showToast("添加成功", "success");
          location.reload();
        }
      });
  });
});
