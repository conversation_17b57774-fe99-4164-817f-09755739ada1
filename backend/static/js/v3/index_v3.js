document.addEventListener("DOMContentLoaded", function () {
  const PAGE_SIZE = 50;
  // 获取DOM元素
  const searchInput = document.getElementById("searchInput");
  const genreFilter = document.getElementById("genreFilter");
  const tagFilterHeader = document.getElementById("tagFilterHeader");
  const tagFilterDropdown = document.getElementById("tagFilterDropdown");
  const tagFilterContent = tagFilterDropdown.querySelector(
    ".tag-filter-content"
  );
  const bookGrid = document.querySelector(".book-grid");
  const resetButton = document.getElementById("resetFilters"); // 重置
  const refreshButton = document.getElementById("refreshBtn"); // 刷新
  const pageSizeSelect = document.getElementById("pageSizeSelect"); // 分页-每页数量
  const prevButton = document.querySelector(".prev-page");
  const nextButton = document.querySelector(".next-page");
  let searchTimer = null;

  const noGenreCheckbox = document.getElementById("noGenreCheckbox"); // 无类型
  const noTagsCheckbox = document.getElementById("noTagsCheckbox"); // 无标签
  const noSubjectCheckbox = document.getElementById("noSubjectCheckbox"); // 无专题
  const myFavoriteCheckbox = document.getElementById("myFavoriteCheckbox"); // 我的收藏
  const noImageCheckbox = document.getElementById("noImageCheckbox"); // 无图
  const addTagBtn = document.getElementById("addTagBtn"); // 添加标签

  // 初始化，计算动态高度
  function adjustOperationBarHeight() {
    const operationBar = document.querySelector(".operation-bar");
    const container = document.querySelector(".container");

    if (operationBar && container) {
      const operationBarHeight = operationBar.offsetHeight;
      const navHeight = 60; // 导航栏 60px
      const collapsedBtn = 20; // 折叠按钮 20px
      container.style.marginTop =
        operationBarHeight + navHeight + collapsedBtn + "px"; // 60px是顶部导航栏的高度
    }
  }

  // 添加标签
  addTagBtn.addEventListener("click", function () {
    // console.log("添加标签");

    // 创建模态框
    const modal = createEditModal("400px");
    modal.show({
      title: "添加标签",
      content: `
        <input type="text" class="modal-input" id="addTagInput" placeholder="请输入标签">
      `,
      footerHtml: `
        <div class="my-flex-end" style="width: 100%;">
          <button class="modal-confirm btn-primary" id="addTagConfirmBtn">确定</button>
        </div>
      `,
    });

    // 绑定确定按钮点击事件
    modal.element
      .querySelector("#addTagConfirmBtn")
      .addEventListener("click", async function () {
        const tag = modal.element.querySelector("#addTagInput").value;
        console.log(tag);

        const fd = new FormData();
        fd.append("tag", tag);

        // 发送请求 fetch
        const response = await fetch(`/v3/api/tag/add/`, {
          method: "POST",
          headers: {
            "X-CSRFToken": getCookie("csrftoken"), // 获取CSRF Token
          },
          body: fd,
        });

        const jo = await response.json();
        if (jo.code !== 0) {
          showToast(jo.msg, "error");
          return;
        }

        // 添加标签成功
        showToast("添加标签成功", "success");
        location.reload();
      });
  });

  // 页面加载时调整高度
  adjustOperationBarHeight();

  // 窗口大小变化时重新调整高度
  window.addEventListener("resize", adjustOperationBarHeight);

  // 查询参数对象
  let params = {
    key: "", // 搜索关键词
    genre: "", // 类型筛选（预留）
    tags: [], // 标签筛选（预留）
    page: 1, // 分页（预留）
    size: PAGE_SIZE, // 每页数量（预留）
    no_genre: 0, // 无类型
    no_tags: 0, // 无标签
    no_subject: 0, // 无专题
    is_collect: 0, // 我的收藏
  };

  // ========== 新增：自动提取url key参数并搜索 ==========
  (function () {
    const urlParams = new URLSearchParams(window.location.search);
    const key = urlParams.get("key");
    if (key) {
      searchInput.value = decodeURIComponent(key);
      params.key = key;
      loadBooks();
    }
  })();

  // 维护一个selectedTags数组来跟踪选中的标签
  let selectedTags = [];

  // 在 DOMContentLoaded 事件开始时创建一个全局的可复用模态框
  let editModal = null;

  // 设置默认 页数
  pageSizeSelect.value = params.size;

  /**
   * 获取 所有专题
   */
  async function loadSubjects() {
    const response = await fetch("/v3/api/book-subjects/");
    const jo = await response.json();
    return jo.data;
  }

  // 添加 操作栏-标签点击事件
  const tagOptions = tagFilterContent.querySelectorAll(
    ".tag-option:not(.disabled)"
  );
  // 遍历标签选项
  tagOptions.forEach((option) => {
    /**
     * 操作栏-标签 点击事件
     */
    option.addEventListener("click", (e) => {
      // 阻止事件冒泡
      e.stopPropagation();
      // 获取标签值
      const value = option.dataset.value;
      // 切换选中状态
      option.classList.toggle("selected");

      // 如果选中，添加到选中标签数组
      if (option.classList.contains("selected")) {
        selectedTags.push(value);
      } else {
        // 如果取消选中，从选中标签数组中移除
        selectedTags = selectedTags.filter((tag) => tag !== value);
      }

      // 更新 操作栏标签 显示
      updateTagFilterHeader();

      // 更新params
      params.tags = selectedTags.length > 0 ? selectedTags.join(";") : [];
      // 重新加载图书
      loadBooks();
    });
  });

  // 监听搜索输入
  searchInput.addEventListener("input", function () {
    if (searchTimer) {
      clearTimeout(searchTimer);
    }
    searchTimer = setTimeout(() => {
      params.key = this.value.trim();
      loadBooks();
    }, 600);
  });

  // 监听类型筛选
  genreFilter.addEventListener("change", function () {
    params.genre = this.value;
    loadBooks();
  });

  // 标签筛选器的展开/收起
  tagFilterHeader.addEventListener("click", (e) => {
    e.stopPropagation();
    tagFilterDropdown.classList.toggle("active");
  });
  // 点击其他地方关闭标签下拉框
  document.addEventListener("click", () => {
    tagFilterDropdown.classList.remove("active");
  });
  // 阻止下拉框内部点击事件冒泡
  tagFilterDropdown.addEventListener("click", (e) => {
    e.stopPropagation();
  });

  // 监听每页条数变化
  pageSizeSelect.addEventListener("change", function () {
    params.size = parseInt(this.value);
    params.page = 1; // 切换每页条数时重置为第一页
    loadBooks();
  });

  // 监听上一页按钮
  prevButton.addEventListener("click", function () {
    if (params.page > 1) {
      params.page--;
      loadBooks();
    }
  });

  // 监听下一页按钮
  nextButton.addEventListener("click", function () {
    params.page++;
    loadBooks();
  });

  // 监听无类型
  noGenreCheckbox.addEventListener("change", function () {
    params.no_genre = this.checked ? 1 : 0;
    loadBooks();
  });

  // 监听无标签
  noTagsCheckbox.addEventListener("change", function () {
    params.no_tags = this.checked ? 1 : 0;
    loadBooks();
  });

  // 监听无专题
  noSubjectCheckbox.addEventListener("change", function () {
    params.no_subject = this.checked ? 1 : 0;
    loadBooks();
  });

  // 监听我的收藏
  myFavoriteCheckbox.addEventListener("change", function () {
    params.is_collect = this.checked ? 1 : 0;
    loadBooks();
  });

  // 监听无图
  noImageCheckbox.addEventListener("change", function () {
    const liImg = document.querySelectorAll(".book-cover img");
    liImg.forEach((img) => {
      img.classList.toggle("hide");
    });
  });

  // 刷新按钮点击事件
  refreshButton.addEventListener("click", function () {
    loadBooks();
  });

  // 重置按钮点击事件
  resetButton.addEventListener("click", function () {
    // 重置所有参数
    params = {
      key: "",
      genre: "",
      tags: [],
      page: 1,
      size: PAGE_SIZE, // 重置为默认的n条
      no_genre: 0,
      no_tags: 0,
      no_subject: 0,
      is_collect: 0, // 重置收藏参数
    };

    // 重置 无图
    noImageCheckbox.checked = false;

    // 重置UI状态
    searchInput.value = "";
    genreFilter.value = "";
    pageSizeSelect.value = params.size; // 重置下拉列表选中值为20

    // 重置标签选择
    selectedTags = [];
    const tagOptions = tagFilterContent.querySelectorAll(".tag-option");
    tagOptions.forEach((option) => {
      option.classList.remove("selected");
    });

    // 更新标签显示
    updateTagFilterHeader();

    // 重置无类型、无标签、无专题
    noGenreCheckbox.checked = false;
    noTagsCheckbox.checked = false;
    noSubjectCheckbox.checked = false;

    // 重置我的收藏
    myFavoriteCheckbox.checked = false;

    loadBooks();
  });

  // 渲染单本图书的html内容
  function renderABookHtml(book) {
    return `
        <div class="book-card" data-id="${book.id}" title="${book.title}">
            <a class="book-cover" href="/v3/book/${book.id}/" target="_blank">
                <img src="${book.first_img}" 
                     alt="${book.title}" 
                     class="${noImageCheckbox.checked ? "hide" : ""}"
                >
                <button class="collect-btn ${book.is_collect ? "active" : ""}" 
                        onclick="toggleCollect(${book.id}, this)">
                    <i class="fas fa-star"></i>
                </button>
            </a>
            <div class="book-info">
                <a href="/v3/book/${book.id}/"
                   class="book-title" >
                    ${book.title}
                </a>

                <div class="book-genre">
                    <span onclick="filterByGenre('${
                      book.genre
                    }')">${book.genre}</span>
                    <button class="edit-genre-btn" onclick="editGenre(${
                      book.id
                    }, event)">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>

                <div class="book-tags-block">
                  <div class="book-tags">
                      ${book.tags
                        .map(
                          (tag) => `
                          <span class="book-single-tag" onclick="filterByTag('${tag.value}')">${tag.value}</span>
                      `
                        )
                        .join("")}
                  </div>

                  <button class="edit-tag-btn" onclick="editTags(${
                    book.id
                  }, event)">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>

                <div id="book-${book.id}--subjects"
                    class="book-subjects-block">
                    ${book.li_subjects
                      .map(
                        (subject) => `
                        <div>
                          <i class="fas fa-layer-group"></i>
                          <span>${subject.title}</span>
                        </div>
                    `
                      )
                      .join("")}
                </div>
                
                <div>
                    <span class="book-page-count--hidden">${
                      book.page_count
                    }</span>
                </div>
            </div>
            <button class="edit-subject-btn" 
              onclick="editSubject(${book.id}, event)" 
              title="修改专题">
                <i class="fas fa-folder"></i>
            </button>
        </div>
    `;
  }

  // 获取并渲染图书数据
  async function loadBooks() {
    try {
      const url = new URL("/v3/api/books/", window.location.origin);

      // 添加所有有效的查询参数
      Object.entries(params).forEach(([key, value]) => {
        if (
          value !== "" &&
          value !== null &&
          !(Array.isArray(value) && value.length === 0)
        ) {
          if (Array.isArray(value)) {
            value.forEach((v) => url.searchParams.append(key, v));
          } else {
            url.searchParams.append(key, value);
          }
        }
      });

      const response = await fetch(url);
      const jo = await response.json();

      if (jo.code !== 0) return;

      const data = jo.data;

      const count = data.count;
      const totalPages = data.total_page;

      // 更新书本数量
      const countElement = document.querySelector(".count-number");
      if (countElement) {
        countElement.textContent = count;
      }

      // 更新分页控件
      updatePagination(totalPages);

      // 更新书本列表
      bookGrid.innerHTML = data.list
        .map((book) => renderABookHtml(book))
        .join("");
    } catch (error) {
      showToast("获取书本数据失败，请稍后重试", "error");
    }
  }

  /**
   * 收藏切换
   * @param {*} bookId
   * @param {*} button
   * @returns
   */
  window.toggleCollect = async function (bookId, button) {
    try {
      // 防止重复点击
      if (button.classList.contains("loading")) {
        return;
      }
      button.classList.add("loading");

      const formData = new FormData();
      formData.append("id", bookId);

      const response = await fetch("/aj_collect/", {
        method: "POST",
        headers: {
          "X-CSRFToken": getCookie("csrftoken"),
        },
        body: formData,
      });

      const jo = await response.json();

      // 检查API返回状态
      if (jo.code !== 0) {
        showToast("收藏失败，请稍后重试", "error");
        return;
      }

      // 更新UI状态
      const isCollected = jo.data === 1;
      button.classList.toggle("active", isCollected);

      // 显示操作结果提示
      const msg = isCollected ? "收藏成功" : "取消成功";
      showToast(msg, "success");

      // 如果当前是在"我的收藏"页面，则需要重新加载列表
      if (params.is_collect === 1) {
        loadBooks();
      }
    } catch (error) {
      showToast("操作失败，请稍后重试", "error");
    } finally {
      button.classList.remove("loading");
    }
  };

  // 按类型筛选功能
  window.filterByGenre = function (genre) {
    params.genre = genre;
    genreFilter.value = genre;
    loadBooks();
  };

  // 书本-标签-点击事件
  window.filterByTag = function (tag) {
    const isSelected = selectedTags.includes(tag);

    if (isSelected) {
      selectedTags = selectedTags.filter((t) => t !== tag);
    } else {
      selectedTags.push(tag);
    }

    // 更新UI状态
    Array.from(tagFilterContent.children).forEach((option) => {
      if (option.dataset.value === tag) {
        option.classList.toggle("selected");
      }
    });

    // 更新 操作栏标签 显示
    updateTagFilterHeader();

    // 更新params
    params.tags = selectedTags.length > 0 ? selectedTags.join(";") : [];
    loadBooks();
  };

  // 更新分页控件
  function updatePagination(totalPages) {
    const currentPage = params.page;
    const pageNumbers = document.querySelector(".page-numbers");

    // 生成页码HTML
    let pageNumbersHtml = "";

    if (totalPages <= 7) {
      // 页数较少时显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pageNumbersHtml += `
          <span class="page-number ${i === currentPage ? "active" : ""}" 
                data-page="${i}">
            ${i}
          </span>
        `;
      }
    } else {
      // 页数较多时显示部分页码
      let pages = [];

      // 始终显示第一页
      pages.push(1);

      if (currentPage > 4) {
        pages.push("...");
      }

      // 显示当前页附近的页码
      for (
        let i = Math.max(2, currentPage - 2);
        i <= Math.min(totalPages - 1, currentPage + 2);
        i++
      ) {
        pages.push(i);
      }

      if (currentPage < totalPages - 3) {
        pages.push("...");
      }

      // 始终显示最后一页
      if (totalPages > 1) {
        pages.push(totalPages);
      }

      // 生成HTML
      pageNumbersHtml = pages
        .map((page) => {
          if (page === "...") {
            return '<span class="page-ellipsis">...</span>';
          }
          return `
          <span class="page-number ${page === currentPage ? "active" : ""}" 
                data-page="${page}">
            ${page}
          </span>
        `;
        })
        .join("");
    }

    pageNumbers.innerHTML = pageNumbersHtml;

    // 更新按钮状态
    prevButton.disabled = currentPage <= 1;
    nextButton.disabled = currentPage >= totalPages;

    // 绑定页码点击事件
    document.querySelectorAll(".page-number").forEach((pageNum) => {
      pageNum.addEventListener("click", function () {
        const page = parseInt(this.dataset.page);
        if (page !== params.page) {
          params.page = page;
          loadBooks();
        }
      });
    });
  }

  /**
   * 随机按钮点击事件
   */
  document
    .getElementById("randomBtn")
    .addEventListener("click", async function () {
      try {
        const url = new URL("/v3/api/books/", window.location.origin);
        url.searchParams.append("is_random", "1");
        url.searchParams.append("size", params.size);

        const response = await fetch(url);
        const jo = await response.json();

        if (jo.code !== 0) return;

        const data = jo.data;
        // 更新书本列表
        bookGrid.innerHTML = data.list
          .map((book) => renderABookHtml(book))
          .join("");
      } catch (error) {
        showToast("获取随机数据失败，请稍后重试", "error");
      }
    });

  /**
   * 修改 书本-类型
   * @param {*} bookId
   * @param {*} event
   */
  window.editGenre = async function (bookId, event) {
    event.stopPropagation(); // 阻止事件冒泡，防止触发filterByGenre
    try {
      // 从 htmlgenreFilter 中获取类型数据
      const genreFilter = document.getElementById("genreFilter");

      // 获取当前图书的类型
      const currentGenre = event.target
        .closest(".book-genre")
        .querySelector("span").textContent;

      const genreListHtml = Array.from(genreFilter.options)
        .filter((option) => option.value !== "")
        .map(
          (option) =>
            `<div class="modal-genre-item ${
              option.value === currentGenre ? "active" : ""
            }" 
                     data-value="${option.value}">
                    ${option.value}
                </div>`
        )
        .join("");

      // 创建修改类型的模态框
      const modal = createEditModal();
      modal.show({
        title: "修改类型",
        content: `
                <div class="modal-genre-list">
                    ${genreListHtml}
                </div>
            `,
      });

      // 绑定类型点击事件
      modal.element.querySelectorAll(".modal-genre-item").forEach((item) => {
        item.addEventListener("click", async () => {
          const newGenre = item.dataset.value;

          const fd = new FormData();
          fd.append("genre", newGenre);

          try {
            // 发送修改请求
            const updateResponse = await fetch(
              `/v3/api/book/${bookId}/genre/update/`,
              {
                method: "POST",
                headers: {
                  "X-CSRFToken": getCookie("csrftoken"), // 获取CSRF Token
                },
                body: fd,
              }
            );

            const updateResult = await updateResponse.json();

            if (updateResult.code === 0) {
              showToast("类型修改成功", "success");
              modal.close();
              loadBooks(); // 重新加载图书列表
            } else {
              showToast(updateResult.msg || "修改失败", "error");
            }
          } catch (error) {
            showToast("修改类型失败，请稍后重试", "error");
          }
        });
      });
    } catch (error) {
      showToast("获取类型数据失败，请稍后重试", "error");
    }
  };

  /**
   * 修改 书本-标签
   */
  window.editTags = async function (bookId, event) {
    event.stopPropagation();
    try {
      // 从 tagFilterContent 获取所有的标签
      const tagFilterContent = tagFilterDropdown.querySelector(
        ".tag-filter-content"
      );
      const liTags = Array.from(
        tagFilterContent.querySelectorAll(".tag-option")
      ).map(
        // 从 data-value 属性获取标签的 tag.value
        (option) => ({
          value: option.dataset.value,
          count: option.dataset.count,
        })
      );

      // 从 .book-tags 找出当前图书的标签
      const bookTags = event.target
        .closest(".book-tags-block")
        .querySelector(".book-tags");
      const liBookTags = Array.from(bookTags.querySelectorAll("span")).map(
        (span) => span.textContent
      );

      // 判断每一个标签，是否在 liBookTags 数组中，是则设置 active 类
      const tagListHtml = liTags
        .map(
          (tag) => `<div class="modal-edit-tag-item ${
            liBookTags.includes(tag.value) ? "active" : ""
          }" 
                     data-value="${tag.value}"
                     alt="${tag.value} (${tag.count})">
                    ${tag.value} (${tag.count})
                </div>`
        )
        .join("");

      // 使用复用的模态框
      const modal = createEditModal();
      modal.show({
        title: "修改标签",
        content: `
                <div class="modal-edit-tags-list">
                    ${tagListHtml}
                </div>
            `,
        footerHtml: `
            <div class="my-flex-end" style="width: 100%;">
              <button class="modal-confirm btn-primary" id="editTagsConfirmBtn">确定</button>
            </div>
        `,
      });

      // 绑定 标签点击事件
      modal.element.querySelectorAll(".modal-edit-tag-item").forEach((item) => {
        item.addEventListener("click", () => {
          item.classList.toggle("active");
        });
      });

      /**
       * 确定按钮 修改-书本-标签
       */
      modal.element
        .querySelector("#editTagsConfirmBtn")
        .addEventListener("click", async () => {
          // 从当前模态框中获取选中的标签
          const selectedTags = Array.from(
            modal.element.querySelectorAll(".modal-edit-tag-item.active") // 修改这里，使用 modal.element 限定范围
          ).map((item) => item.dataset.value);

          // 发送请求 fetch
          const response = await fetch(`/v3/api/book/${bookId}/tags/update/`, {
            method: "POST",
            headers: {
              "X-CSRFToken": getCookie("csrftoken"), // 获取CSRF Token
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              li_tags: selectedTags,
            }),
          });
          const jo = await response.json();
          if (jo.code === 0) {
            showToast("修改标签成功", "success");
            modal.close();
            loadBooks(); // 重新加载图书列表
          } else {
            showToast(jo.msg, "error");
          }
        });
    } catch (error) {
      showToast("操作失败，请稍后重试", "error");
    }
  };

  /**
   * 修改 书本-专题
   * @param {*} bookId
   * @param {*} event
   */
  window.editSubject = async function (bookId, event) {
    event.stopPropagation();
    try {
      // 获取专题列表
      const liSubjects = await loadSubjects();

      // 获取 当前书本的专题：id 为 `book-${bookId}--subjects` 的 所有span的内容，组成数组
      const c = document.getElementById(`book-${bookId}--subjects`);
      const liNowSubjects = Array.from(
        c.querySelectorAll(".book-subjects-block span")
      ).map((subject) => subject.textContent);

      // 使用复用的模态框，遍历 liSubjects，一个专题一个div，主要显示 专题的图片thumb_url，专题title，modal有确定按钮
      const subjectListHtml = liSubjects
        .map(
          (subject) => `
        <div class="modal-subject-item ${
          liNowSubjects.includes(subject.title) ? "active" : ""
        }"
          data-value="${subject.title}">
          <img src="${subject.thumb_url}" alt="${subject.title}">
          <div>${subject.title}</div>
        </div>
      `
        )
        .join("");
      // 创建模态框
      const modal = createEditModal();
      modal.show({
        title: "修改专题",
        content: `
                <div class="modal-subjects-list">
                    ${subjectListHtml}
                </div>
            `,
        footerHtml: `
            <div class="my-flex-end" style="width: 100%;">
              <button class="modal-confirm btn-primary" id="editSubjectConfirmBtn">确定</button>
            </div>
        `,
      });

      // 绑定 专题选中事件
      modal.element.querySelectorAll(".modal-subject-item").forEach((item) => {
        item.addEventListener("click", () => {
          item.classList.toggle("active");
        });
      });

      /**
       * 确定按钮 修改-书本-专题
       */
      modal.element
        .querySelector("#editSubjectConfirmBtn")
        .addEventListener("click", async () => {
          // 从模态框中获取 选中的专题
          const selectedSubjects = Array.from(
            modal.element.querySelectorAll(".modal-subject-item.active")
          ).map((item) => item.dataset.value);

          // 发送请求 fetch
          const response = await fetch(
            `/v3/api/book/${bookId}/subjects/update/`,
            {
              method: "POST",
              headers: {
                "X-CSRFToken": getCookie("csrftoken"), // 获取CSRF Token
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                li_subjects: selectedSubjects,
              }),
            }
          );
          const jo = await response.json();
          if (jo.code === 0) {
            showToast("修改专题成功", "success");
            modal.close();
            loadBooks(); // 重新加载图书列表
          } else {
            showToast(jo.msg, "error");
          }
        });
    } catch (error) {
      console.error(error);
      showToast("操作失败，请稍后重试", "error");
    }
  };

  // 初始化
  loadBooks(); // 获取初始图书数据

  // 初始化操作栏折叠功能
  initOperationBarCollapse();

  // 更新标签筛选器的显示文本
  function updateTagFilterHeader() {
    const placeholder = tagFilterHeader.querySelector(".placeholder");

    if (selectedTags.length === 0) {
      placeholder.textContent = "全部标签";
      return;
    }

    if (selectedTags.length <= 3) {
      placeholder.textContent = selectedTags.join(",");
    } else {
      placeholder.textContent = `${selectedTags.slice(0, 3).join(",")}...(${
        selectedTags.length
      })`;
    }
  }

  /**
   * 初始化操作栏折叠功能
   */
  function initOperationBarCollapse() {
    const operationBar = document.querySelector(".operation-bar");
    const toggleBtn = document.querySelector(".operation-toggle");

    // 绑定折叠/展开事件
    toggleBtn.addEventListener("click", () => {
      operationBar.classList.toggle("collapsed");
      toggleBtn.classList.toggle("collapsed");

      // 保存状态到 localStorage
      localStorage.setItem(
        "operationBarCollapsed",
        operationBar.classList.contains("collapsed")
      );

      // 折叠/展开后重新调整容器margin-top
      adjustOperationBarHeight();
    });

    // 恢复折叠状态
    const isCollapsed =
      localStorage.getItem("operationBarCollapsed") === "true";
    if (isCollapsed) {
      operationBar.classList.add("collapsed");
      toggleBtn.classList.add("collapsed");

      // 初始化时如果是折叠状态，也要调整容器margin-top
      adjustOperationBarHeight();
    }
  }

  // ================= 合并模式相关 =================
  let mergeMode = false;
  let selectedBooks = [];

  // 合并按钮事件
  const mergeBtn = document.getElementById("mergeBtn");
  const mergeBtnGroup = document.getElementById("mergeBtnGroup");
  const mergeCancelBtn = document.getElementById("mergeCancelBtn");
  const mergeSubmitBtn = document.getElementById("mergeSubmitBtn");
  const mergeModal = document.getElementById("mergeModal");
  const mergeBookList = document.getElementById("mergeBookList");
  const mergeModalSubmit = document.getElementById("mergeModalSubmit");

  // 进入合并模式
  mergeBtn &&
    mergeBtn.addEventListener("click", function () {
      mergeMode = true;
      selectedBooks = [];
      mergeBtn.style.display = "none";
      mergeBtnGroup.style.display = "flex";
      // 给所有书本加可勾选UI
      document.body.classList.add("merge-mode");
      updateBookSelectable();
    });
  // 取消合并模式
  mergeCancelBtn &&
    mergeCancelBtn.addEventListener("click", function () {
      mergeMode = false;
      selectedBooks = [];
      mergeBtn.style.display = "";
      mergeBtnGroup.style.display = "none";
      document.body.classList.remove("merge-mode");
      updateBookSelectable();
    });
  // 提交合并，弹出模态框
  mergeSubmitBtn &&
    mergeSubmitBtn.addEventListener("click", function () {
      if (selectedBooks.length < 2) {
        showToast("请至少选择2本图书", "error");
        return;
      }
      renderMergeModalList();
    });
  // 模态框提交
  mergeModalSubmit &&
    mergeModalSubmit.addEventListener("click", function () {
      // 获取排序后的id
      let sortedIds = [];
      mergeBookList.querySelectorAll(".merge-book-item").forEach(function (el) {
        sortedIds.push(Number(el.dataset.id));
      });
      console.log(sortedIds);
      mergeModal.style.display = "none";
      // 可重置合并模式
      mergeMode = false;
      selectedBooks = [];
      mergeBtn.style.display = "";
      mergeBtnGroup.style.display = "none";
      document.body.classList.remove("merge-mode");
      updateBookSelectable();
    });

  // 书本勾选逻辑
  function updateBookSelectable() {
    document.querySelectorAll(".book-card").forEach(function (card) {
      if (mergeMode) {
        card.classList.add("merge-selectable");
        card.classList.remove("selected");
        card.querySelector(".book-cover").removeAttribute("href");
      } else {
        card.classList.remove("merge-selectable");
        card.classList.remove("selected");
        card
          .querySelector(".book-cover")
          .setAttribute("href", `/v3/book/${card.dataset.id}/`);
      }
    });
  }

  // 监听书本点击
  bookGrid.addEventListener("click", function (e) {
    if (!mergeMode) return;
    let card = e.target.closest(".book-card");
    if (!card) return;
    e.preventDefault();
    const bookId = Number(card.dataset.id);
    if (card.classList.contains("selected")) {
      card.classList.remove("selected");
      selectedBooks = selectedBooks.filter((id) => id !== bookId);
    } else {
      card.classList.add("selected");
      if (!selectedBooks.includes(bookId)) selectedBooks.push(bookId);
    }
  });

  // 渲染模态框列表
  function renderMergeModalList() {
    // 使用自定义模态框
    const modal = createEditModal("400px");
    let html = "";
    selectedBooks.forEach((id) => {
      const card = document.querySelector(`.book-card[data-id='${id}']`);
      const title = card.querySelector(".book-title").textContent;
      const img = card.querySelector("img").getAttribute("src");
      html += `<div class=\"merge-book-item\" data-id=\"${id}\">
                <img src=\"${img}\" style=\"width:40px;height:40px;object-fit:cover;margin-right:10px;vertical-align:middle;\" />
                <span style=\"vertical-align:middle;\">${title}</span>
                </div>`;
    });
    modal.show({
      title: "合并图书顺序调整",
      content: `<div id='mergeBookList'>${html}</div>`,
      footerHtml: `<div class="my-flex-end" style="width: 100%;">
                    <button id='mergeModalSubmit' class='btn-success'>提交</button>
                  </div>`,
    });
    // 绑定拖拽排序（使用Sortable.js）
    setTimeout(() => {
      const mergeBookList = document.getElementById("mergeBookList");
      if (window.Sortable) {
        Sortable.create(mergeBookList, {
          animation: 150,
        });
      }
      // 绑定按钮事件
      document.getElementById("mergeModalSubmit").onclick = async function () {
        let sortedIds = [];
        mergeBookList
          .querySelectorAll(".merge-book-item")
          .forEach(function (el) {
            sortedIds.push(Number(el.dataset.id));
          });
        // 提交到后端API
        try {
          const response = await fetch("/v3/api/book/merge/", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "X-CSRFToken": getCookie("csrftoken"),
            },
            body: JSON.stringify({ li_book_id: sortedIds }),
          });
          const jo = await response.json();
          if (jo.code === 0) {
            showToast("合并成功", "success");
            modal.close();
            // 可重置合并模式
            mergeMode = false;
            selectedBooks = [];
            mergeBtn.style.display = "";
            mergeBtnGroup.style.display = "none";
            document.body.classList.remove("merge-mode");
            updateBookSelectable();
            // 可选：刷新列表
            loadBooks();
          } else {
            showToast(jo.msg || "合并失败", "error");
          }
        } catch (e) {
          showToast("网络错误，合并失败", "error");
        }
      };
    }, 0);
  }

  // 每次渲染书本后，更新可勾选状态
  const oldLoadBooks = loadBooks;
  loadBooks = async function () {
    await oldLoadBooks.apply(this, arguments);
    updateBookSelectable();
  };
});
