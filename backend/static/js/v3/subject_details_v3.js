document.addEventListener("DOMContentLoaded", function () {
    const sortBtn = document.querySelector(".sort-btn");
    const sortActions = document.querySelector(".sort-actions");
    const sortConfirm = document.querySelector(".sort-confirm");
    const sortCancel = document.querySelector(".sort-cancel");
    const bookList = document.querySelector(".book-list");
    const subjectId = document.querySelector(".subject-detail").dataset.id;
    let sortable = null;

    // 加载图册列表
    async function loadBooks() {
        try {
            const response = await fetch(`/v3/api/subject/${subjectId}/book/list/`);
            const result = await response.json();

            if (result.code !== 0) {
                throw new Error(result.msg || "加载失败");
            }

            renderBooks(result.data);
        } catch (error) {
            console.error("加载图册失败：", error);
            bookList.innerHTML = '<div class="no-data">加载失败，请刷新重试</div>';
        }
    }

    // 渲染图册列表
    function renderBooks(books) {
        if (!books || books.length === 0) {
            bookList.innerHTML = '<div class="no-data">暂无内容</div>';
            return;
        }

        const html = books.map(book => `
            <div class="book-item" data-id="${book.id}">
                <a href="/v3/book/${book.id}/" title="${book.title}">
                    <div class="book-thumb">
                        <img src="${book.first_img}"
                             alt="${book.title}"
                             width="100%"
                             height="100%">
                    </div>
                    <div class="book-info">
                        <div class="book-title">${book.title}</div>
                        <div class="my-small-gray-text">
                            <i class="fa fa-image"></i>
                            ${book.page_count}
                        </div>
                    </div>
                </a>
            </div>
        `).join('');

        bookList.innerHTML = html;
    }

    // 初始化Sortable
    function initSortable() {
        return Sortable.create(bookList, {
            animation: 150,
            handle: ".book-item",
            onStart: function () {
                // 开始拖动时的处理
                bookList.classList.add("sorting");
            },
            onEnd: function () {
                // 结束拖动时的处理
                bookList.classList.remove("sorting");
            }
        });
    }

    // 启用排序模式
    function enableSortMode() {
        sortBtn.style.display = "none";
        sortActions.classList.add("active");
        // 禁用所有链接点击
        bookList.querySelectorAll("a").forEach(a => {
            a.style.pointerEvents = "none";
        });
        // 初始化拖拽
        sortable = initSortable();
        // 添加排序模式样式
        bookList.classList.add("sort-mode");
    }

    // 禁用排序模式
    function disableSortMode() {
        sortBtn.style.display = "flex";
        sortActions.classList.remove("active");
        // 恢复链接点击
        bookList.querySelectorAll("a").forEach(a => {
            a.style.pointerEvents = "auto";
        });
        // 销毁拖拽实例
        if (sortable) {
            sortable.destroy();
            sortable = null;
        }
        // 移除排序模式样式
        bookList.classList.remove("sort-mode");
    }

    // 获取排序后的ID列表
    function getSortedIds() {
        return Array.from(bookList.children)
            .filter(el => el.dataset.id)
            .map(el => el.dataset.id);
    }

    // 获取CSRF Token
    function getCSRFToken() {
        const name = "csrftoken";
        let cookieValue = null;
        if (document.cookie && document.cookie !== "") {
            const cookies = document.cookie.split(";");
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + "=")) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // 提交排序结果到后端
    async function submitSort(sortedIds) {
        try {
            const response = await fetch(`/v3/api/subject/${subjectId}/books/sort/`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRFToken": getCSRFToken(),
                },
                body: JSON.stringify({
                    book_ids: sortedIds
                })
            });

            const jo = await response.json();
            if (jo.code !== 0) {
                throw new Error(jo.msg || "排序失败");
            }

            // 成功，页面刷新
            location.reload();
        } catch (error) {
            console.error("排序出错：", error);
            alert("排序保存失败，请重试！");
        }
    }

    // 点击排序按钮
    if (sortBtn) {
        sortBtn.addEventListener("click", function () {
            enableSortMode();
        });
    }

    // 点击确认按钮
    if (sortConfirm) {
        sortConfirm.addEventListener("click", async function () {
            const sortedIds = getSortedIds();
            console.log("排序确认，ID顺序：", sortedIds);

            // 禁用确认按钮，防止重复提交
            sortConfirm.disabled = true;

            // 调用API保存排序
            await submitSort(sortedIds);

            // 恢复确认按钮状态
            sortConfirm.disabled = false;

            disableSortMode();
        });
    }

    // 点击取消按钮
    if (sortCancel) {
        sortCancel.addEventListener("click", function () {
            loadBooks();  // 重新加载数据而不是刷新页面
            disableSortMode();
        });
    }

    // 初始加载数据
    loadBooks();
}); 