// dom 加载完成
document.addEventListener("DOMContentLoaded", function () {
  // 获取图书ID
  const bookId = document.getElementById("bookId").textContent;
  // 获取图书标题
  const bookTitle = document.querySelector(
    ".book-base-info--title"
  ).textContent;

  // 所有图片的父容器
  const imgBox = document.querySelector("#viewer");
  // 横竖屏 按钮元素
  const btnHorizontal = document.querySelector("#btnHorizontal");
  const btnVertical = document.querySelector("#btnVertical");
  // 获取滑块元素
  const sizeSlider = document.querySelector("#sizeSlider");
  const sizeValue = document.querySelector(".size-slider-value");

  // 获取收藏按钮
  const collectBtn = document.querySelector(".book-base-info--collect");
  // 获取下载按钮
  const downloadBtn = document.querySelector(".book-base-info--download");
  // 获取删除按钮
  const deleteBtn = document.querySelector(".book-base-info--delete");

  // 获取尺寸调节按钮
  const decreaseBtn = document.getElementById("decreaseSize");
  const increaseBtn = document.getElementById("increaseSize");

  // 获取编辑按钮组元素
  const editGroup = document.querySelector(".book-base-info--edit-group");
  const editButton = editGroup.querySelector(".book-base-info--edit");
  const confirmButton = editGroup.querySelector(".book-base-info--confirm");
  const cancelButton = editGroup.querySelector(".book-base-info--cancel");

  // 编辑模式状态（编辑模式支持：图片删除、图片排序）
  let isEditMode = false;

  // 拖动组件
  let sortable = null;

  // 编辑按钮点击事件
  editButton.addEventListener("click", function () {
    isEditMode = !isEditMode;

    if (isEditMode) {
      editGroup.classList.add("editing"); // 编辑模式 ui
      // 添加编辑模式类名到图片容器
      document.querySelector("#viewer").classList.add("edit-mode");

      // 初始化 拖动组件
      sortable = new Sortable(document.querySelector("#viewer"), {
        animation: 150,
        sort: true,
      });
    } else {
      editGroup.classList.remove("editing"); // 编辑模式 ui

      // 移除编辑模式类名
      document.querySelector("#viewer").classList.remove("edit-mode");

      // 移除 拖动组件
      sortable.destroy();
    }
  });

  // 确认按钮点击事件
  confirmButton.addEventListener("click", async function () {
    exitEditMode();

    // 找出排序后的图片id数组，获取 viewer div img 的 data-page-id 属性，组成数组
    const imgIds = Array.from(imgBox.querySelectorAll("#viewer img")).map(
      (img) => {
        return img.dataset.pageId;
      }
    );

    // 发送请求到 v3/api/book/<book_id>/page/all_sort/
    const response = await fetch(`/v3/api/book/${bookId}/page/all_sort/`, {
      method: "POST",
      headers: {
        "X-CSRFToken": getCookie("csrftoken"),
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ li_page_id: imgIds }),
    });

    const jo = await response.json();
    if (jo.code !== 0) {
      showToast(jo.msg || "排序失败", "error");
    }

    // 成功，页面刷新
    location.reload();
  });

  // 取消按钮点击事件
  cancelButton.addEventListener("click", function () {
    // 恢复排序前的图片顺序（页面刷新）
    location.reload();
  });

  /**
   * 退出编辑模式
   */
  function exitEditMode() {
    isEditMode = false;
    editGroup.classList.remove("editing");
    // 移除编辑模式类名
    document.querySelector("#viewer").classList.remove("edit-mode");

    // 移除 拖动组件
    if (sortable) {
      sortable.destroy();
      sortable = null;
    }
  }

  // 删除按钮点击事件
  document
    .querySelector("#viewer")
    .addEventListener("click", async function (e) {
      const deleteBtn = e.target.closest(".book-img-delete-btn");
      if (deleteBtn && isEditMode) {
        const pageId = deleteBtn.dataset.pageIdDelBtn;
        // 发送删除请求
        const response = await fetch(
          `/v3/api/book/${bookId}/page/${pageId}/del/`,
          {
            method: "POST",
            headers: {
              "X-CSRFToken": getCookie("csrftoken"),
            },
          }
        );

        const jo = await response.json();
        if (jo.code !== 0) {
          throw new Error(jo.msg);
        }

        // 删除 图片盒子dom
        const imgBox = document.querySelector(`[data-img-box-id="${pageId}"]`);
        if (imgBox) {
          imgBox.remove();
        }
      }
    });

  // 初始化 图片懒加载
  let ob = lozad(); // lazy loads elements with default selector as '.lozad'
  ob.observe();

  // 初始化 图片预览插件
  const viewer = new Viewer(document.getElementById("viewer"), {
    title: true,
    rotatable: false,
    scalable: false,
    fullscreen: true,
    zoomRatio: 0.5,
    url: "data-src",
  });

  // 图片尺寸系数
  let imgLength = null;
  let imgDirection = null; // 0-横屏 1-竖屏

  // 初始化，如果发现是移动端，则默认使用 竖屏模式
  if (!isMobile()) {
    // pc端
    imgDirection = 0;

    imgLength = {
      0: 24, // 0-横屏：高度系数
      1: 60, // 1-竖屏：宽度系数
    };
  } else {
    // 移动端
    imgDirection = 1;

    imgLength = {
      0: 60, // 横屏默认 滑动初始值 60%
      1: 100, // 竖屏默认不需要宽度，直接 width:100%
    };
  }

  // 绑定收藏按钮点击事件
  collectBtn.addEventListener("click", function () {
    toggleCollect(this);
  });

  // 绑定下载按钮点击事件
  downloadBtn.addEventListener("click", function () {
    handleDownload(this);
  });

  // 绑定删除按钮点击事件
  deleteBtn.addEventListener("click", function () {
    handleDelete(this);
  });

  /**
   * 下载书本
   */
  async function handleDownload(button) {
    // 发送请求到 v3/api/book/<book_id>/download/
    const response = await fetch(`/v3/api/book/${bookId}/download/`, {
      method: "GET",
    });

    // 从 django api 的 FileResponse 中下载附件
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${bookTitle}.zip`;
    a.click();
  }

  /**
   * 删除书本
   */
  async function handleDelete(button) {
    // 发送请求
    const response = await fetch(`/v3/api/book/${bookId}/del/`, {
      method: "POST",
      headers: {
        "X-CSRFToken": getCookie("csrftoken"),
      },
    });

    const jo = await response.json();
    if (jo.code !== 0) {
      throw new Error(jo.msg);
    }

    showToast("删除成功，3s内关闭页面", "success");
    setTimeout(() => {
      // location.href = "/v3/";
      window.close()
    }, 3000);
  }

  /**
   * 更新尺寸值
   * @param {number} value 新的尺寸值
   */
  function updateSize(value) {
    // 确保值在0-100之间
    value = Math.max(0, Math.min(100, value));

    // 更新滑块和显示值
    sizeSlider.value = value;
    sizeValue.textContent = value;

    // 更新图片尺寸
    imgLength[imgDirection] = parseInt(value);
    setImgLength();
  }

  // 监听减小按钮点击
  decreaseBtn.addEventListener("click", function () {
    const currentValue = parseInt(sizeSlider.value);
    updateSize(currentValue - 1); // 每次减小1
  });

  // 监听增大按钮点击
  increaseBtn.addEventListener("click", function () {
    const currentValue = parseInt(sizeSlider.value);
    updateSize(currentValue + 1); // 每次增加1
  });

  // 监听滑块值变化
  sizeSlider.addEventListener("input", function () {
    updateSize(this.value);
  });

  // 设置初始值
  sizeSlider.value = imgLength[imgDirection];
  sizeValue.textContent = imgLength[imgDirection];

  /**
   * 收藏切换
   * @param {*} button
   * @returns
   */
  async function toggleCollect(button) {
    try {
      // 防止重复点击
      if (button.classList.contains("loading")) {
        return;
      }
      button.classList.add("loading");

      const formData = new FormData();
      formData.append("id", bookId);

      const response = await fetch("/aj_collect/", {
        method: "POST",
        headers: {
          "X-CSRFToken": getCookie("csrftoken"),
        },
        body: formData,
      });

      const jo = await response.json();

      // 检查API返回状态
      if (jo.code !== 0) {
        showToast("收藏失败，请稍后重试", "error");
        return;
      }

      // 更新UI状态
      const isCollected = jo.data === 1;
      button.classList.toggle("active", isCollected);

      // 显示操作结果提示
      const msg = isCollected ? "收藏成功" : "取消成功";
      showToast(msg, "success");
    } catch (error) {
      showToast("操作失败，请稍后重试", "error");
    } finally {
      button.classList.remove("loading");
    }
  }

  /**
   * 根据 imgDirection 值，更新ui样式
   */
  window.renderUiByDirection = function () {
    const btnHorizontal = document.querySelector("#btnHorizontal");
    const btnVertical = document.querySelector("#btnVertical");
    if (imgDirection === 0) {
      // 横屏
      btnHorizontal.className = "btn-primary";
      btnVertical.className = "btn-normal";

      // 横屏 图片盒子使用 .book-content-img-box
      document.querySelector("#viewer").className = "book-content-img-box";

      // 横屏 图片使用 .single-book-img
      document.querySelectorAll("#viewer img").forEach((img) => {
        img.className = "single-book-img lozad";
      });
    } else {
      // 竖屏
      btnVertical.className = "btn-primary";
      btnHorizontal.className = "btn-normal";

      // 竖屏 图片盒子使用 .book-content-img-box--vertical
      document.querySelector("#viewer").className =
        "book-content-img-box--vertical";

      // 竖屏 图片使用 .single-book-img--vertical
      document.querySelectorAll("#viewer img").forEach((img) => {
        img.className = "single-book-img--vertical lozad";
      });
    }

    // 更新viewer
    viewer.update();
  };

  /**
   * 横屏 按钮点击事件
   */
  btnHorizontal.addEventListener("click", function (event) {
    imgDirection = 0; // 设置方向
    renderUiByDirection(); // 触发渲染ui
    // 更新滑块值
    sizeSlider.value = imgLength[0];
    sizeValue.textContent = imgLength[0];
    setImgLength();
  });

  /**
   * 竖屏 按钮点击事件
   */
  btnVertical.addEventListener("click", function (event) {
    imgDirection = 1;
    renderUiByDirection();
    // 更新滑块值
    sizeSlider.value = imgLength[1];
    sizeValue.textContent = imgLength[1];
    setImgLength();
  });

  /**
   * 修改书本标题
   * @param {*} event
   */
  window.editTitle = async function (event) {
    event.stopPropagation();

    // 获取当前title
    const currentTitle = document.querySelector(
      ".book-base-info--title"
    ).textContent;

    // 创建模态框
    const modal = createEditModal((width = "400px"));
    modal.show({
      title: "修改标题",
      content: `
        <input type="text" class="modal-input" value="${currentTitle}">
      `,
      footerHtml: `
        <div class="my-flex-end" style="width: 100%;">
          <button class="modal-confirm btn-primary" id="editTitleConfirmBtn">确定</button>
        </div>
      `,
    });

    // 绑定确定按钮点击事件
    modal.element
      .querySelector("#editTitleConfirmBtn")
      .addEventListener("click", async () => {
        const newTitle = modal.element.querySelector(".modal-input").value;

        const fd = new FormData();
        fd.append("title", newTitle);

        const response = await fetch(`/v3/api/book/${bookId}/title/update/`, {
          method: "POST",
          headers: {
            "X-CSRFToken": getCookie("csrftoken"),
          },
          body: fd,
        });

        const updateJo = await response.json();
        if (updateJo.code !== 0) {
          showToast(updateJo.msg || "修改失败", "error");
          location.reload();
          return;
        }

        // 修改成功
        showToast("修改标题成功", "success");
        modal.close();

        // 更新页面显示
        document.querySelector(".book-base-info--title").textContent = newTitle;
      });
  };
  /**
   * 修改书本类型
   * @param {*} event
   */
  window.editGenre = async function (event) {
    event.stopPropagation();
    try {
      // 获取所有类型
      const response = await fetch("/v3/api/book-genres/");
      const jo = await response.json();
      if (jo.code !== 0) {
        throw new Error(jo.msg);
      }

      // 获取当前类型
      const currentGenre = document
        .querySelector(".book-genre-value")
        .textContent.trim();

      // 创建类型选择列表HTML
      const genreListHtml = jo.data
        .map(
          (genre) => `
        <div class="modal-genre-item ${
          currentGenre === genre.value ? "active" : ""
        }"
             data-value="${genre.value}">
          ${genre.value} (${genre.count})
        </div>
      `
        )
        .join("");

      // 使用模态框
      const modal = createEditModal();
      modal.show({
        title: "修改类型",
        content: `
        <div class="modal-genre-list">
          ${genreListHtml}
        </div>
      `,
      });

      // 绑定类型点击事件
      modal.element.querySelectorAll(".modal-genre-item").forEach((item) => {
        item.addEventListener("click", async () => {
          // 移除其他选中状态
          modal.element
            .querySelectorAll(".modal-genre-item")
            .forEach((i) => i.classList.remove("active"));
          // 添加当前选中状态
          item.classList.add("active");

          // 当前的值
          const currentValue = item.dataset.value;

          // 发送更新请求
          const fd = new FormData();
          fd.append("genre", currentValue);
          const updateResponse = await fetch(
            `/v3/api/book/${bookId}/genre/update/`,
            {
              method: "POST",
              headers: {
                "X-CSRFToken": getCookie("csrftoken"),
              },
              body: fd,
            }
          );

          const updateJo = await updateResponse.json();
          if (updateJo.code === 0) {
            showToast("修改类型成功", "success");
            // 更新页面显示 book-genre-value
            document.querySelector(".book-genre-value").textContent =
              currentValue;
            modal.close();
          } else {
            showToast(updateJo.msg || "修改失败", "error");
          }
        });
      });
    } catch (error) {
      console.error(error);
      showToast("操作失败，请稍后重试", "error");
    }
  };

  /**
   * 修改-书本-专题
   * @param {*} event
   */
  window.editSubjects = async function (event) {
    event.stopPropagation();
    try {
      // 获取所有专题
      const response = await fetch("/v3/api/book-subjects/");
      const liSubjectsJo = await response.json();
      if (liSubjectsJo.code !== 0) {
        throw new Error(liSubjectsJo.msg);
      }
      const liSubjects = liSubjectsJo.data;
      // 获取当前图书的专题
      const liNowSubjects = Array.from(
        document.querySelectorAll(".book-single-subject")
      ).map((subject) => subject.title);

      const subjectListHtml = liSubjects
        .map(
          (subject) => `
        <div class="modal-subject-item ${
          liNowSubjects.includes(subject.title) ? "active" : ""
        }"
             data-value="${subject.title}">
          <img src="${subject.thumb_url}" alt="${subject.title}">
          <div>${subject.title}</div>
        </div>
      `
        )
        .join("");

      // 创建模态框
      const modal = createEditModal("80%");
      modal.show({
        title: "修改专题",
        content: `
          <div class="modal-subjects-list">
            ${subjectListHtml}
          </div>
        `,
        footerHtml: `
          <div class="my-flex-end" style="width: 100%;">
            <button class="modal-confirm btn-primary" id="editSubjectsConfirmBtn">确定</button>
          </div>
        `,
      });

      // 绑定专题点击事件
      modal.element.querySelectorAll(".modal-subject-item").forEach((item) => {
        item.addEventListener("click", () => {
          item.classList.toggle("active");
        });
      });

      // 确认按钮点击事件
      modal.element
        .querySelector("#editSubjectsConfirmBtn")
        .addEventListener("click", async () => {
          // 获取选中的专题
          const selectedSubjects = Array.from(
            modal.element.querySelectorAll(".modal-subject-item.active")
          ).map((item) => item.dataset.value);

          // 发送更新请求
          const response = await fetch(
            `/v3/api/book/${bookId}/subjects/update/`,
            {
              method: "POST",
              headers: {
                "X-CSRFToken": getCookie("csrftoken"),
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                li_subjects: selectedSubjects,
              }),
            }
          );

          const updateResult = await response.json();
          if (updateResult.code === 0) {
            showToast("专题修改成功", "success");
            modal.close();

            location.reload();
          }
        });
    } catch (error) {
      console.error(error);
      showToast("操作失败，请稍后重试", "error");
    }
  };

  /**
   * 修改书本标签
   */
  window.editTags = async function (event) {
    event.stopPropagation();
    try {
      // 获取所有标签
      const response = await fetch("/v3/api/book-tags/");
      const jo = await response.json();
      if (jo.code !== 0) {
        throw new Error(jo.msg);
      }

      // 获取当前图书的标签
      const bookTagsContainer = document.querySelector(
        ".book-info--other-item--tags"
      );
      const currentTags = Array.from(
        bookTagsContainer.querySelectorAll(".book-single-tag")
      ).map((tag) => tag.textContent);

      // 创建标签选择列表HTML
      const tagListHtml = jo.data
        .map(
          (tag) => `
          <div class="modal-edit-tag-item ${
            currentTags.includes(tag.value) ? "active" : ""
          }" 
               data-value="${tag.value}"
               alt="${tag.value} (${tag.count})">
            ${tag.value} (${tag.count})
          </div>
        `
        )
        .join("");

      // 使用模态框
      const modal = createEditModal();
      modal.show({
        title: "修改标签",
        content: `
          <div class="modal-edit-tags-list">
            ${tagListHtml}
          </div>
        `,
        footerHtml: `
          <div class="my-flex-end" style="width: 100%;">
            <button class="modal-confirm btn-primary" id="editTagsConfirmBtn">确定</button>
          </div>
        `,
      });

      // 绑定标签点击事件
      modal.element.querySelectorAll(".modal-edit-tag-item").forEach((item) => {
        item.addEventListener("click", () => {
          item.classList.toggle("active");
        });
      });

      // 确认按钮点击事件
      modal.element
        .querySelector("#editTagsConfirmBtn")
        .addEventListener("click", async () => {
          // 获取选中的标签
          const selectedTags = Array.from(
            modal.element.querySelectorAll(".modal-edit-tag-item.active")
          ).map((item) => item.dataset.value);

          // 发送更新请求
          const response = await fetch(`/v3/api/book/${bookId}/tags/update/`, {
            method: "POST",
            headers: {
              "X-CSRFToken": getCookie("csrftoken"),
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              li_tags: selectedTags,
            }),
          });

          const updateResult = await response.json();
          if (updateResult.code === 0) {
            showToast("标签修改成功", "success");

            // 更新页面显示的标签
            bookTagsContainer.innerHTML = selectedTags
              .map((tag) => `<div class="book-single-tag">${tag}</div>`)
              .join("");

            modal.close();
          } else {
            showToast(updateResult.msg || "修改失败", "error");
          }
        });
    } catch (error) {
      console.error(error);
      showToast("操作失败，请稍后重试", "error");
    }
  };

  /**
   * 根据 横竖屏配合 imgLength，动态计算图片的高度或者宽度，并设置到 图片上
   */
  function setImgLength() {
    let tag = "";
    let styleStr = "";

    // ———— 移动端 小屏 ————
    if (isMobile()) {
      if (imgDirection === 0) {
        // 横屏
        val = imgLength[0] * 2 + "px"; // 横屏，限制高度

        styleStr = `height: ${val}`;
      } else {
        // 竖屏
        val = imgLength[1] + "%"; // 竖屏，限制宽度

        styleStr = `width: ${val}`;
      }
    } else {
      // ———— 大屏 ————
      if (imgDirection === 0) {
        // 横屏
        val = imgLength[0] * 10 + "px"; // 横屏，限制高度

        styleStr = `height: ${val}`;
      } else {
        // 竖屏
        val = imgLength[1] * 10 + "px"; // 竖屏，限制宽度

        styleStr = `width: ${val}`;
      }
    }

    // 设置 图片尺寸系数
    const liImgBox = document.querySelectorAll("#viewer img");
    liImgBox.forEach((img) => {
      img.style.cssText = styleStr;
    });

    // 更新viewer
    viewer.update();
  }

  // 渲染 横竖屏 ui
  renderUiByDirection();

  // 设置 图片尺寸系数
  setImgLength();
});
