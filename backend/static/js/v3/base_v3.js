/**
 * 获取CSRF Token的辅助函数
 * @param {*} name
 * @returns
 */
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== "") {
        const cookies = document.cookie.split(";");
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === name + "=") {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

/**
 * 创建一个可复用的模态框实例
 * width 可为空，默认800px
 * @returns
 */
function createEditModal(width) {
    const modal = document.createElement("div");
    modal.className = "modal";

    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title"></div>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer"></div>
        </div>
    `;

    document.body.appendChild(modal);

    // 设置宽度
    if (!width) {
        width = '800px'
    }
    modal.querySelector('.modal-content').style.width = width;

    // 关闭模态框
    const close = () => {
        modal.classList.remove("show");
        setTimeout(() => {
            modal.style.display = "none";
            // 清理事件监听器
            modal.querySelector(".modal-body").innerHTML = "";
            modal.querySelector(".modal-footer").innerHTML = "";
        }, 300);
    };

    // 绑定关闭事件
    modal.querySelector(".modal-close").addEventListener("click", close);
    modal.addEventListener("click", (e) => {
        if (e.target === modal) close();
    });

    // ESC关闭
    window.addEventListener("keydown", (e) => {
        if (e.key === "Escape" && modal.style.display === "block") close();
    });

    editModal = {
        show: (options = {}) => {
            const {title = "", content = "", footerHtml = ""} = options;
            modal.querySelector(".modal-title").textContent = title;
            modal.querySelector(".modal-body").innerHTML = content;
            modal.querySelector(".modal-footer").innerHTML = footerHtml;
            modal.style.display = "block";
            requestAnimationFrame(() => modal.classList.add("show"));
        },
        close,
        element: modal,
    };

    return editModal;
}

/**
 * 提示框
 * @param {*} message
 * @param {*} type
 * @param {*} duration
 */
function showToast(message, type = "success", duration = 3000) {
    // 移除现有的提示框
    const existingToast = document.querySelector(".toast");
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的提示框
    const toast = document.createElement("div");
    toast.className = `toast ${type}`;

    // 添加图标
    const icon = type === "success" ? "fa-check-circle" : "fa-times-circle";
    toast.innerHTML = `
            <i class="fas ${icon}"></i>
            <span>${message}</span>
        `;

    document.body.appendChild(toast);

    // 添加显示动画
    requestAnimationFrame(() => {
        toast.classList.add("show");
    });

    // 自动隐藏
    setTimeout(() => {
        toast.classList.remove("show");
        setTimeout(() => toast.remove(), 300);
    }, duration);
}

/**
 * 判断是否移动端
 */
function isMobile() {
    return document.body.clientWidth < 768;
}
