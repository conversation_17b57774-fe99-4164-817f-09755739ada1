.subject-detail {
    padding: 16px;
    margin-top: 50px;
}

/* 头部区域包装器 */
.subject-header-wrapper {
    display: flex;
    justify-content: space-between;
    background: #fff;
}

/* 专题信息区域 */
.subject-info {
    flex: 1;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
}

.subject-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
}

.subject-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.subject-book-count {
    font-size: 12px;
    color: #999;
    margin-left: 4px;
}

.subject-cover {
    max-width: 200px;
}

.subject-cover img {
    object-fit: contain;
    width: 100%;
    height: auto;
    max-height: 200px;
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px;
}

.subject-create-time {
    color: #999;
    font-size: 12px;
    margin-top: 8px;
    text-align: left;
}

/* 操作栏 */
.action-bar {
    display: flex;
    gap: 10px;
    padding-top: 5px;
    min-width: 50px;
}

.sort-btn,
.sort-confirm,
.sort-cancel {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    color: #666;
    transition: all 0.2s ease;
}

.sort-btn i,
.sort-confirm i,
.sort-cancel i {
    font-size: 16px;
}

.sort-btn:hover,
.sort-confirm:hover,
.sort-cancel:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #333;
}

.sort-actions {
    display: none;
}

.sort-actions.active {
    display: flex;
}

.sort-btn {
    color: #4a90e2;
}

.sort-confirm {
    color: #4caf50;
}

.sort-cancel {
    color: #f44336;
}

/* 图书列表 */
.book-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.book-item {
    background: #fff;
    border-radius: 6px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px,
    rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.book-item:hover {
    transform: translateY(-2px);
    box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px,
    rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
    border: 1px solid gray;
}

.book-item a {
    display: block;
    text-decoration: none;
    color: inherit;
}

.book-thumb {
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
}

.book-thumb img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.book-info {
    padding: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.book-title {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 150px;
}

.no-data {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: #666;
    font-size: 15px;
}

/* 排序模式样式 */
.sort-mode .book-item {
    cursor: move;
    user-select: none;
}

.sort-mode .book-item:hover {
    transform: none;
}

.sorting .book-item {
    opacity: 0.8;
}

.sortable-ghost {
    opacity: 0.5;
    background: #f5f5f5;
}

.sortable-chosen {
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: #666;
    font-size: 15px;
}

.loading::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #666;
    border-right-color: transparent;
    border-radius: 50%;
    animation: loading-rotate 0.8s linear infinite;
}

@keyframes loading-rotate {
    from {
        transform: rotate(0);
    }
    to {
        transform: rotate(360deg);
    }
}
