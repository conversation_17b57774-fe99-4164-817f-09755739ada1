/* 响应式布局调整 */
@media (max-width: 768px) {
  .operation-bar {
    padding: 4px 0;
  }
  .operation-container {
    padding: 0 4px;
  }
  .operation-toggle {
    top: 64px;
  }

  .book-grid {
    gap: 8px;
    padding: 8px;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
  .book-card {
    /* width: 100px; */
    width: unset;
    border-radius: 4px;
  }
  .book-cover img {
    height: 110px;
  }
}
