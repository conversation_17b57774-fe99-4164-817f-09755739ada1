/* 响应式布局 */
@media (max-width: 768px) {
  .subject-detail {
    padding: unset;
  }

  .subject-header-wrapper {
    padding: 12px;
  }

  .subject-info {
    margin-right: 0;
  }

  .action-bar {
    flex-direction: row;
    justify-content: center;
    padding-top: 10px;
  }

  .sort-actions.active {
    flex-direction: row;
  }

  .subject-title {
    font-size: 20px;
    max-width: 280px;
    overflow-x: auto;
  }

  .book-list {
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 8px;
    padding: 8px;
  }

  .book-thumb {
    height: 100px;
  }

  .book-item a {
    padding: unset;
  }

  .book-title {
    font-size: 14px;
    margin: 0 0 4px;
  }

  .book-count {
    font-size: 12px;
  }
}

