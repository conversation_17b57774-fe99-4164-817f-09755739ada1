/* 基础样式重置 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-left {
  height: 100%;
}

.nav-logo {
  line-height: 60px;
  padding: 0 8px;
}
.nav-logo a {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: bold;
  color: #333;
  text-decoration: unset;
}
.nav-logo:hover span {
  text-decoration: underline;
}

.nav-logo i {
  margin-right: 8px;
  color: #1a73e8;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  line-height: 50px;
}

.nav-links a {
  text-decoration: none;
  color: #666;
  font-size: 1rem;
  display: flex;
  align-items: center;
  transition: color 0.3s ease;
  padding: 0 16px;
}

.nav-links a i {
  margin-right: 6px;
}

.nav-links a:hover {
  color: #00a59c;
  text-decoration: underline;
}

.nav-links a.active {
  color: white;
  font-weight: 500;
  background-color: #00a59c;
  border-radius: 8px;
}

.hide {
  /* visibility: hidden; */
  display: none;
}

/* 滚到 顶部/底部 */
.go-to-up-btn,
.go-to-down-btn {
  position: fixed;
  right: 4px;
  bottom: 80px;
  background: #52c41a;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9999;
}

.go-to-down-btn {
  bottom: 48px;
}

.go-to-up-btn i,
.go-to-down-btn i {
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.go-to-up-btn:hover,
.go-to-down-btn:hover {
  background: #89d464;
  transform: scale(1.1);
}

/* 按钮基础样式 */
.btn-primary,
.btn-danger,
.btn-success,
.btn-warning,
.btn-normal {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  font-weight: 500;
}

/* 主要按钮 - 柔和的蓝色 */
.btn-primary {
  background-color: #4a90e2;
  color: white;
  box-shadow: 0 1px 2px rgba(74, 144, 226, 0.15);
}

.btn-primary:hover {
  background-color: #357abd;
}

.btn-primary:active {
  background-color: #2f6dad;
  transform: scale(0.98);
}

/* 危险按钮 - 柔和的红色 */
.btn-danger {
  background-color: #ff7875;
  color: white;
  box-shadow: 0 1px 2px rgba(255, 120, 117, 0.15);
}

.btn-danger:hover {
  background-color: #ff5652;
}

.btn-danger:active {
  background-color: #f54d4d;
  transform: scale(0.98);
}

/* 成功按钮 - 柔和的绿色 */
.btn-success {
  background-color: #52c41a;
  color: white;
  box-shadow: 0 1px 2px rgba(82, 196, 26, 0.15);
}

.btn-success:hover {
  background-color: #49ad17;
}

.btn-success:active {
  background-color: #419e15;
  transform: scale(0.98);
}

/* 警告按钮 - 柔和的橙色 */
.btn-warning {
  background-color: #faad14;
  color: #2c2c2c;
  box-shadow: 0 1px 2px rgba(250, 173, 20, 0.15);
}

.btn-warning:hover {
  background-color: #e89f0e;
}

.btn-warning:active {
  background-color: #d48806;
  transform: scale(0.98);
}

/* 普通按钮 - 浅白色 */
.btn-normal {
  background-color: #ffffff;
  color: #666666;
  border: 1px solid #d9d9d9;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-normal:hover {
  background-color: #fafafa;
  border-color: #999999;
  color: #333333;
}

.btn-normal:active {
  background-color: #f5f5f5;
  border-color: #999999;
  color: #333333;
  transform: scale(0.98);
}

/* 禁用状态 */
.btn-primary:disabled,
.btn-danger:disabled,
.btn-success:disabled,
.btn-warning:disabled,
.btn-normal:disabled {
  opacity: 0.65;
  cursor: not-allowed;
  pointer-events: none;
}

.btn-groups {
  display: flex;
  /* gap: 4px; */
}

.btn-groups button {
  /* margin-right: 4px; */
  border-radius: 2px;
}

.btn-groups button:active {
  transform: unset;
}

.base-hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  opacity: 1;
}

.modal-input {
  width: 100%;
  box-sizing: border-box;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.modal-content {
  position: relative;
  background-color: #fff;
  width: 90%;
  /*max-width: 800px;*/
  margin: 100px auto;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-20px);
  transition: transform 0.3s ease;
}

.modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal-close {
  cursor: pointer;
  padding: 5px;
  color: #999;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #666;
}

.modal-body {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  max-height: 600px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  border-top: 1px solid #eee;
  padding-top: 16px;
  margin-top: 16px;
}

/* 提示框样式 */
.toast {
  position: fixed;
  top: 80px; /* 导航栏高度 + 间距 */
  right: 20px;
  transform: translateX(120%);
  padding: 12px 20px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1100;
}

.toast.show {
  transform: translateX(0);
}

/* 成功提示样式 */
.toast.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

.toast.success i {
  color: #67c23a;
}

/* 错误提示样式 */
.toast.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
}

.toast.error i {
  color: #f56c6c;
}

/* book 单个标签样式 */
.book-single-tag {
  padding: 0px 4px;
  background-color: #28a745;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0; /* 防止标签被压缩 */
}

.my-flex-xy {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my-flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.my-flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.my-small-gray-text {
  font-size: 12px;
  color: #999999;
}

.nav-left {
  display: flex;
  align-items: center;
}
.nav-search {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.nav-search input {
  padding: 6px 14px;
  border-radius: 20px;
  border: 1px solid #ddd;
  font-size: 15px;
  width: 200px;
  background: #fafbfc;
  transition: border 0.2s, box-shadow 0.2s;
  outline: none;
  color: #222;
  box-shadow: none;
}

.nav-search input:focus {
  border-color: #0e447b;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.12);
  background: #fff;
}

.nav-search input:hover {
  border-color: #0e447b;
}
