.book-details-container {
  padding: 70px 20px 8px;
}

.book-base-info-block {
  /* position: relative; */
  display: flex;
  justify-content: space-between;
}

.book-base-info {
  flex: 1;
}

.book-base-info--right-btn-block {
  width: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.book-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

#bookId {
  display: none;
}

.book-base-info--title {
  font-size: 20px;
  font-weight: 600;
}

.book-info--other {
  font-size: 14px;
  color: gray;
}

.book-info--other-item {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 4px;
}

.book-info--other-item > div:first-child {
  width: 40px;
  margin-right: 4px;
  font-size: 13px;
  color: gray;
}

.book-info--other-item--tags {
  display: flex;
  justify-content: flex-start;
  gap: 4px;
}

.book-base-info--collect {
  color: #908f8f;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s ease;
}

.book-base-info--collect:hover {
  transform: scale(1.1);
  color: #f86055;
}

.book-base-info--collect.active {
  color: #f86055;
}

.book-base-info--collect.loading {
  opacity: 0.5;
  pointer-events: none;
}

.book-base-info--download {
  color: #259b18;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.3s ease;
}

.book-base-info--download:hover {
  transform: scale(1.1);
  color: #5fbc55;
}

.book-base-info--edit-group {
}
.book-base-info--edit-group.editing .book-base-info--edit {
  display: none;
}
.book-base-info--edit-group.editing .book-base-info--edit-actions {
  display: flex;
}

.book-base-info--edit,
.book-base-info--confirm,
.book-base-info--cancel {
  cursor: pointer;
  /* font-size: 18px; */
  transition: all 0.3s ease;
}

.book-base-info--edit {
  color: #4a90e2;
}
.book-base-info--edit:hover {
  transform: scale(1.1);
  color: #357abd;
}

/**
  默认隐藏，编辑模式下，确认和取消按钮 才显示
*/
.book-base-info--edit-actions {
  display: none;
  gap: 8px;
}

.book-base-info--confirm {
  color: #28a745;
}
.book-base-info--confirm:hover {
  transform: scale(1.1);
  color: #218838;
}

.book-base-info--cancel {
  color: #dc3545;
}
.book-base-info--cancel:hover {
  transform: scale(1.1);
  color: #c82333;
}

.book-base-info--delete {
  color: #ffbcbc;
  cursor: pointer;
}

.book-content-img-box {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 图片包装器样式 */
.book-img-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
}

/* 删除按钮样式 */
.book-img-delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 20;
}

.book-img-delete-btn:hover {
  background-color: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

/* 编辑模式下显示删除按钮 */
.edit-mode .book-img-delete-btn {
  opacity: 1;
}

/* 竖屏样式 */
.book-content-img-box--vertical {
  display: flex;
  flex-direction: column;
  padding-top: 8px;
}

.single-book-img {
  border: 1px solid #d0d0d0;
  border-radius: 2px;
  cursor: pointer;
}
.single-book-img:hover {
  border: 1px solid #797979;
}

.single-book-img--vertical {
  width: 100%;
  margin: 0 auto;
  border: 1px solid #d0d0d0;
  cursor: pointer;
}
.single-book-img--vertical:hover {
  border: 1px solid #797979;
}

/* 尺寸滑块容器 */
.size-slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 尺寸调节按钮 */
.size-slider-btn {
  width: 16px;
  height: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.size-slider-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
  color: #4a90e2;
}

.size-slider-btn:active {
  background: #e8e8e8;
  transform: scale(0.95);
}

/* 滑块样式 */
.size-slider {
  -webkit-appearance: none;
  width: 120px;
  height: 4px;
  border-radius: 2px;
  background: #ddd;
  outline: none;
}

.size-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  transition: all 0.2s ease;
}

.size-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  background: #357abd;
}

.size-slider-value {
  min-width: 24px;
  font-size: 13px;
  color: #666;
}

/* book 类型 */
.book-genre-value {
  color: #df660e;
  font-weight: 600;
}

/* 类型选择列表样式 */
.modal-genre-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}

.modal-genre-item {
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  text-align: center;
  transition: all 0.2s ease;
}

.modal-genre-item:hover {
  background: #e8f3ff;
  color: #4a90e2;
}

.modal-genre-item.active {
  background: #4a90e2;
  color: white;
}

.book-title-block {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.edit-book-title-btn {
  margin-left: 24px;
  cursor: pointer;
  color: #4a90e2;
  visibility: hidden;
}
.book-title-block:hover .edit-book-title-btn {
  visibility: visible;
}

.edit-book-genre-btn {
  margin-left: 24px;
  cursor: pointer;
  color: #4a90e2;
  /* display: none; */
  visibility: hidden;
}
.genre-line:hover .edit-book-genre-btn {
  visibility: visible;
}

.edit-book-genre-btn:hover {
  color: #87bdf2;
}

.edit-book-tags-btn {
  margin-left: 24px;
  cursor: pointer;
  color: #4a90e2;
  visibility: hidden;
}
.tags-line:hover .edit-book-tags-btn {
  visibility: visible;
}

.edit-book-tags-btn:hover {
  color: #87bdf2;
}

.subject-line:hover .edit-book-subject-btn {
  visibility: visible;
}

.edit-book-subject-btn {
  margin-left: 24px;
  cursor: pointer;
  color: #4a90e2;
  visibility: hidden;
}
.edit-book-subject-btn:hover {
  color: #87bdf2;
}

/* 模态框-修改标签-列表容器 */
.modal-edit-tags-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}
/* 模态框-修改标签-子项目 */
.modal-edit-tag-item {
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.modal-edit-tag-item:hover {
  background: #e8f3ff;
  color: #4a90e2;
}
.modal-edit-tag-item.active {
  background: #4a90e2;
  color: white;
}

/* 相关专题部分样式 */
.related-subjects-section {
  margin: 16px 0;
}

.related-subject-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.related-subject-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.subject-icon {
  color: #4a90e2;
  margin-right: 6px;
}

.related-subject-link {
  color: #4a90e2;
  text-decoration: none;
  font-size: 16px;
  transition: all 0.2s ease;
}

.related-subject-link:hover {
  color: #357abd;
  text-decoration: underline;
}

.same-subject-box {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.same-subject-box__book-link {
  text-decoration: none;
}
.same-subject-box__book {
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  text-decoration: none;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #d0d0d0;
  transition: all 0.3s ease;
  text-decoration: unset;
  padding: 2px;
}

.same-subject-box__book:hover {
  transform: translateY(-3px);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px,
    rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
  text-decoration: underline;
  border: 1px solid #464646;
}

.same-subject-book-thumb {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.same-subject-book-title {
  margin: 0 4px;
  font-size: 13px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.same-subject-box__book__active {
  display: flex;
  flex-wrap: wrap;
  background-color: #45496a;
}
.same-subject-box__book__active .same-subject-book-title {
  color: white;
}

.subject-divider {
  margin: 16px 0;
  border: 0;
  border-top: 1px solid #eee;
}

/* 书籍导航（上一册/下一册）样式 */
.book-navigation {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.book-navigation a {
  color: #4a90e2;
  text-decoration: none;
  transition: all 0.2s ease;
}

.book-navigation a:hover {
  color: #357abd;
  text-decoration: underline;
}

.book-single-subject {
  margin-right: 4px;
  text-decoration: none;
  color: #1f00bc;
  font-weight: 600;
}
.book-single-subject:hover {
  text-decoration: underline;
}

.modal-subjects-list {
  padding: 4px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 16px;
}
.modal-subject-item {
  min-width: 80px;
  padding: 4px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  text-align: center;
  transition: all 0.2s ease;
  border: 1px solid #c5c5c5;
}

.modal-subject-item img {
  width: 100px;
  height: 100px;
  object-fit: cover;
}

.modal-subject-item div {
  font-size: 12px;
  color: #666;
}

.modal-subject-item.active {
  background: #4a90e2;
  color: white;
}

.modal-subject-item.active div {
  color: white;
}
