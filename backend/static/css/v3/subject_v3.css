.subject-list {
  display: flex;
  gap: 12px;
  padding: 8px;
  flex-wrap: wrap;
  margin: 70px 0;
}

.subject-item {
  background: #fff;
  border-radius: 6px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px,
    rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(150, 150, 150, 0.05);
}

.subject-item:hover {
  transform: translateY(-2px);
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px,
    rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
  border: 1px solid gray;
}

.subject-item:hover .subject-title{
  text-decoration: underline;
}

.subject-item a {
  display: block;
  text-decoration: none;
  color: inherit;
}

.subject-thumb {
  width: 100%;
  height: 180px;
  min-width: 140px;
  overflow: hidden;
  border-radius: 4px;
}

.subject-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.subject-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px;
}

.subject-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  
}


.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  color: #666;
  font-size: 15px;
}

.sort-cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.sort-btn,
.add-subject-btn {
  position: fixed;
  right: 4px;
  border: none;
  border-radius: 2px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  padding: unset;
  background-color: #4a90e2;
  color: white;
  z-index: 9999;
}

.add-subject-btn {
  top: 180px;
}
.sort-btn {
  top: 240px;
}

.sort-btn:hover,
.add-subject-btn:hover {
  transform: scale(1.1);
  opacity: 0.9;
}

.sort-confirm {
  border: none;
  border-radius: 2px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  padding: unset;
  background-color: #56cd5b;
  color: white;
  z-index: 9999;
  margin-right: 2px;
}

.sort-cancel {
  border: none;
  border-radius: 2px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  padding: unset;
  background-color: #fe6a60;
  color: white;
  z-index: 9999;
}

.sort-actions {
  display: none;
}

.sort-actions.active {
  display: flex;
  position: fixed;
  top: 240px;
  right: 4px;
}

/* 排序模式样式 */
.sort-mode .subject-item {
  cursor: move;
  user-select: none;
}

.sort-mode .subject-item:hover {
  transform: none;
}

.sorting .subject-item {
  opacity: 0.8;
}

.sortable-ghost {
  opacity: 0.5;
  background: #f5f5f5;
}

.sortable-chosen {
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
