/* 为了防止导航栏遮挡内容，给container添加上边距 */
.container {
  /* margin-top 将由 JavaScript 动态计算，以适应不同设备 */
}

/* 修改操作栏样式 */
.operation-bar {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  padding: 8px 0;
  z-index: 999;
}

.operation-container {
  /* max-width: 1200px; */
  padding: 0 20px;
  display: flex;
  flex-wrap: wrap;
  /* gap: 16px; */
  align-items: center;
}

/* 操作项通用样式 */
.operation-item {
  margin-right: 16px;
  margin-bottom: 4px;
}

.opertaion-item-check-box {
  border: 1px solid #c9c9c9;
  border-radius: 4px;
  padding: 4px;
}
.opertaion-item-check-box label {
  font-size: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666666;
}

/* 搜索框样式调整 */
.search-box {
  max-width: 200px;
}

/* 筛选框样式调整 */
.filter-box {
  max-width: 200px;
  position: relative;
}

.filter-box select {
  width: 100%;
  padding: 8px 12px; /* 移除左边距，因为不需要图标了 */
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  background-color: white;
  cursor: pointer;
}

/* 多选select的特殊样式 */
.filter-box select[multiple] {
  height: 38px; /* 设置固定高度 */
  min-height: 38px;
  padding-right: 12px;
  overflow-y: auto; /* 添加垂直滚动条 */
}

/* 当focus时展开显示选项 */
.filter-box select[multiple]:focus {
  height: 160px; /* 展开后的固定高度 */
  position: absolute; /* 使展开的选项浮动在其他元素之上 */
  width: 100%;
  background: white;
  z-index: 1000;
}

/* 单选select保持下拉箭头 */
.filter-box select:not([multiple]) {
  appearance: none;
  -webkit-appearance: none;
  padding-right: 30px; /* 为下拉箭头留出空间 */
}

/* 只给单选select添加下拉箭头图标 */
.filter-box:has(select:not([multiple]))::after {
  content: "\f078";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
}

/* select选项样式 */
.filter-box select option {
  padding: 4px 8px; /* 稍微减小选项的padding */
  font-size: 14px;
}

.filter-box select:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.filter-genre-block {
  position: relative;
  display: inline-block;
}
.filter-genre-block i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: #666;
}
.filter-genre-block select {
  padding-left: 24px;
}

/* 重置按钮样式 */
.btn-reset i {
  font-size: 12px; /* 图标稍微小一点 */
}

/* 保持其他样式不变 */
.search-box {
  position: relative;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-box input {
  box-sizing: border-box;
  padding: 8px 12px 8px 35px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.3s ease;
}

/* 图书网格样式 */
.book-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 24px;
  padding: 20px;
}

.book-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  width: 160px;
  position: relative;
  border: 1px solid #eeeeee;
  box-sizing: border-box;
}
.book-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid #919191;
  transform: scale(1.01); /* 鼠标悬停时放大效果 */
}
.book-card:hover .book-subjects-block {
  visibility: visible;
}
.book-card:hover .book-title {
  overflow: unset;
}

.book-cover {
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid #eeeeee;
}

.book-cover img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.book-info {
  padding: 0 4px 4px;
}

.book-title {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  cursor: pointer;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 160px;
  text-decoration: unset;
}

.book-title:hover {
  color: #0017aa;
  text-decoration: underline;
}

.book-genre {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 11px;
  color: #666;
  transition: color 0.2s;
  height: 15px;
}

.book-genre span {
  color: #df660e;
  font-weight: 600;
  cursor: pointer;
}
.book-genre span:hover {
  text-decoration: underline;
}
.book-genre:hover .edit-genre-btn {
  display: flex;
}

.book-tags-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 15px;
}

.book-tags-block:hover .edit-tag-btn {
  display: flex;
}

/* 标签容器样式 */
.book-tags {
  display: flex;
  justify-content: flex-start;
  gap: 4px;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.book-tags::-webkit-scrollbar {
  display: none;
}

.book-page-count--hidden {
  display: none;
}

/* 收藏按钮样式 */
.collect-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.collect-btn i {
  font-size: 12px;
  color: #ccc;
  transition: all 0.3s ease;
}

.collect-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.collect-btn.active i {
  color: #f86055;
}

.collect-btn:hover i {
  color: #f86055;
}

.collect-btn:active {
  transform: scale(0.9);
}

/* 标签筛选器样式 */
.tag-filter {
  position: relative;
}

.tag-filter-header {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  background-color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag-filter-header i {
  color: #666;
}

.tag-filter-header:hover {
  border-color: #1a73e8;
}

.tag-filter-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 300px; /* 设置为header的两倍宽度 */
  margin-top: 4px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: none;
  padding: 4px;
}

.tag-filter-dropdown.active {
  display: block;
}

.tag-filter-content {
  padding: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.tag-option {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.tag-option:hover {
  background-color: #e0e0e0;
}

.tag-option.selected {
  background-color: #1a73e8;
  color: white;
}

/* 标签选项禁用状态 */
.tag-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
  color: #999;
  pointer-events: none; /* 禁止鼠标事件 */
}

.tag-option.disabled:hover {
  background-color: #f5f5f5; /* 禁止hover效果 */
}

/* 收藏按钮加载状态 */
.collect-btn.loading {
  pointer-events: none;
  opacity: 0.6;
}

.book-count {
  font-size: 12px;
  color: #666;
  padding: 4px 0;
}

/* 分页与数量容器 */
.count-number {
  color: #409eff;
  font-weight: bold;
}

.pagination-box {
  display: flex;
  /* flex-direction: column; */
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  padding: 0px 20px;
}

/* 分页控件样式 */
.pagination {
  display: inline-flex; /* 改为inline-flex */
  align-items: center;
  gap: 4px;
}

/* 分页按钮样式 */
.pagination button {
  padding: 1px 3px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background-color: white;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
  min-width: 20px;
  text-align: center;
}

.pagination button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination button:not(:disabled):hover {
  border-color: #409eff;
  color: #409eff;
}

/* 页码容器样式 */
.page-numbers {
  display: flex;
  gap: 2px;
  padding: 0 2px;
}

/* 美化滚动条 */
.page-numbers::-webkit-scrollbar {
  height: 2px;
}

.page-numbers::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.page-numbers::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.page-number {
  padding: 2px 4px;
  min-width: 20px;
  text-align: center;
  font-size: 11px;
  cursor: pointer;
  border-radius: 3px;
  color: #666;
  transition: all 0.2s;
}

.page-number:hover:not(.active) {
  background-color: #f0f0f0;
}

.page-number.active {
  background-color: #409eff;
  color: white;
}

/* 分页省略号样式 */
.page-ellipsis {
  padding: 2px 4px;
  min-width: 20px;
  text-align: center;
  font-size: 11px;
  color: #666;
}

/* 页面大小控制样式 */
.page-size-control {
  margin-left: 6px; /* 减小左边距 */
  font-size: 11px; /* 减小字体大小 */
  color: #666;
}

.page-size-control select {
  border-color: #cecece;
  border-radius: 4px;
}

/* 悬浮提示框样式 */
.book-tooltip {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  width: 200px;
  font-size: 12px;
  pointer-events: none;
  transition: opacity 0.2s;
  border: 1px solid #eee;
}

/* 每行数据的容器 */
.book-tooltip > div {
  display: flex;
  padding: 4px 0;
  line-height: 1.4;
  border-bottom: 1px solid #f5f5f5;
}

.book-tooltip > div:last-child {
  border-bottom: none;
}

/* 左侧标签样式 */
.book-tooltip > div::before {
  content: attr(data-label);
  width: 32px;
  color: #999;
  flex-shrink: 0;
}

/* 右侧内容样式 */
.book-tooltip > div > span {
  flex: 1;
  color: #333;
  margin-left: 8px;
}

/* 标签容器样式 */
.book-tooltip .tooltip-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 单个标签样式 */
.book-tooltip .tooltip-tag {
  background: #f0f0f0;
  padding: 1px 6px;
  border-radius: 3px;
  color: #666;
  font-size: 11px;
}

/* 修改类型按钮样式 */
.edit-genre-btn,
.edit-tag-btn {
  display: none;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
  margin-left: 4px;
}

.edit-genre-btn i,
.edit-tag-btn i {
  font-size: 12px;
  color: #4a90e2;
  transition: transform 0.2s ease;
}

.edit-genre-btn:hover i {
  transform: scale(1.1);
}
.edit-tag-btn:hover i {
  transform: scale(1.1);
}

/* 类型选择列表样式 */
.modal-genre-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}

.modal-genre-item {
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  text-align: center;
  transition: all 0.2s ease;
}

.modal-genre-item:hover {
  background: #e8f3ff;
  color: #4a90e2;
}

.modal-genre-item.active {
  background: #4a90e2;
  color: white;
}

/* 模态框-修改标签-列表容器 */
.modal-edit-tags-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}
/* 模态框-修改标签-子项目 */
.modal-edit-tag-item {
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.modal-edit-tag-item:hover {
  background: #e8f3ff;
  color: #4a90e2;
}
.modal-edit-tag-item.active {
  background: #4a90e2;
  color: white;
}

/* 专题编辑按钮样式 */
.edit-subject-btn {
  position: absolute;
  top: 4px;
  right: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  visibility: hidden;
}

.edit-subject-btn i {
  font-size: 12px;
  color: #4a90e2;
  transition: transform 0.2s ease;
}

.edit-subject-btn:hover {
  border-color: #4a90e2;
  background: #f0f7ff;
}

.edit-subject-btn:hover i {
  transform: scale(1.1);
}

/* 鼠标悬浮在书本卡片上时显示编辑按钮 */
.book-card:hover .edit-subject-btn {
  visibility: visible;
}

.modal-subjects-list {
  padding: 4px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 16px;
}

.modal-subject-item {
  padding: 4px;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  text-align: center;
  transition: all 0.2s ease;
  border: 1px solid #c5c5c5;
}

.modal-subject-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.modal-subject-item div {
  font-size: 12px;
  color: #666;
}

.modal-subject-item.active {
  background: #4a90e2;
  color: white;
}

.modal-subject-item.active div {
  color: white;
}

/* 操作栏折叠/展开按钮 */
.operation-toggle {
  position: fixed;
  right: 4px;
  top: 70px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e48d12;
  border: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
}

.operation-toggle i {
  font-size: 14px;
  color: white;
  transition: transform 0.3s ease;
}

.operation-toggle:hover {
  background: #e9ad58;
  transform: scale(1.1);
}

/* 操作栏折叠状态 */
.operation-bar.collapsed {
  height: 0;
  padding: 0;
  overflow: hidden;
}

.operation-bar.collapsed + .container {
  /* margin-top值将由JavaScript动态计算 */
}

.operation-toggle.collapsed i {
  transform: rotate(180deg);
}

.book-subjects-block {
  font-size: 12px;
  color: #666;
  position: absolute;
  visibility: hidden;
  top: 0;
  left: 0;
  background-color: black;
  border-radius: 2px;
  opacity: 0.7;
}
.book-subjects-block div {
  color: white;
  padding: 2px;
}

.add-tag {
  text-align: right;
}
.add-tag i {
  font-size: 12px;
  color: #1a73e8;
  cursor: pointer;
}
.add-tag i:hover {
  color: #0056b3;
}

/* 合并模式下书本可勾选样式 */
body.merge-mode .book-card.merge-selectable {
  cursor: pointer;
  border: 1px dashed #e3e3e3;
  opacity: 0.95;
}
body.merge-mode .book-card.merge-selectable.selected {
  background: #e3f2fd;
  border: 1px solid #1976d2;
  box-shadow: 0 0 0 3px #1976d2;
  position: relative;
}
body.merge-mode .book-card.merge-selectable.selected::after {
  content: "\2714";
  position: absolute;
  top: 8px;
  left: 8px;
  background: #1976d2;
  color: #fff;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  z-index: 2;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 合并模态框样式 */
#mergeModal.modal {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
#mergeModal .modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 24px 24px 12px 24px;
  min-width: 340px;
  min-height: 120px;
  max-width: 90vw;
  max-height: 80vh;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  position: relative;
}
#mergeBookList {
  margin: 18px 0 0 0;
  min-height: 40px;
  max-height: 320px;
  overflow-y: auto;
}
.merge-book-item {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 6px 12px;
  cursor: grab;
  border: 1px solid #e0e0e0;
  transition: background 0.2s, box-shadow 0.2s;
}
.merge-book-item.dragElem {
  opacity: 0.5;
  background: #bbdefb;
}
.merge-book-item.over {
  border: 2px dashed #1976d2;
  background: #e3f2fd;
}
.merge-book-item img {
  border-radius: 4px;
  margin-right: 10px;
}
#mergeModal .modal-footer {
  margin-top: 20px;
  text-align: right;
}

#mergeCancelBtn {
  cursor: pointer;
  background: #fff;
  color: #6c757d;
  border: 1px solid #6c757d;
  border-radius: 4px;
  padding: 2px 14px;
  font-size: 13px;
  /* margin-right: 8px; */
  transition: background 0.2s, color 0.2s, border 0.2s;
}
#mergeCancelBtn:hover {
  background: #f8f9fa;
  color: #495057;
  border-color: #495057;
}

#mergeBtnGroup{
  /* display: flex; */
  /* align-items: center; */
}