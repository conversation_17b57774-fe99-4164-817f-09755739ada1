/*小于 移动设备标准*/
@media screen and (max-width: 768px) {
    .book-block {
        margin: 4px;
    }

    .book-img-block {
        max-width: 100px;
        max-height: 100px;
    }

    .book-block--title {
        max-width: 100px;
        font-size: 11px;
    }

    /**
    类型与标签 在移动端宽度下，改为纵向排列
     */
    .genre-and-tags-block {
        flex-direction: column;
        align-items: flex-start;
    }


    .book-genre-block {
        max-width: 100px;
    }

    .book-tags-block {
        max-width: 100px;
    }

    .main-book-info {
        padding: unset;
    }


    /**
    专题
     */
    .subject-block {
        margin: 8px;
    }

    .subject-block img {
        max-width: 100px;
        max-height: 120px;
        /*height: 80px;*/
    }

    .subject-block--title {
        font-size: 14px;
        font-weight: 600;
        max-width: 100px;
    }

    .subject-block--title a {
        color: black;
    }

    .el-input-number--mini {
        width: 100px;
    }

    .opr_btn_line {
        width: 200px;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
    }

    .el-dialog__body {
        padding: 4px !important;
    }

    .same-subject-box__book {
        padding: 2px;
        /*max-width: 140px;*/
        overflow: hidden;
        white-space: nowrap;
    }

    /* 专题-表格 形式 */
    .subject-block-for-table--book-block {
        width: 48%;
        margin: 4px 2px;
    }

    .subject-block-for-table--book-block img {
        min-width: 40px;
        width: 40px;
        height: 40px;
    }

    .subject-block-for-table--book-block-title {
        -webkit-line-clamp: 2;
    }
}