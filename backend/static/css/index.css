:root {
    --primary-color: #45496A; /* 定义主色 */
    --primary-color-plain: #7D8BAE; /* 定义主浅色 */
    --secondary-color: #E5857B; /* 定义次色 */
    --secondary-color-plain: #F1B2B2; /* 定义次色 */
    --secondary-color-other: #E8CCC7; /* 定义次色 */
    --font-size: 16px; /* 定义字体大小 */
    --padding: 8px; /* 定义内边距 */
}

body {
    color: var(--primary-color) !important;
}


.my_flex_space {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.my_flex_y_center {
    display: flex;
    align-items: center;
}

.my_small_text {
    font-size: 12px;
    color: gray;
}

.back_to_top {
    position: fixed;
    bottom: 50px;
    right: 8px;
    font-size: 32px;
    cursor: pointer;
    line-height: normal;
    opacity: 0.5;
    color: #a01eef;
}

.back_to_top:hover {
    color: black;
}


.go_to_end {
    position: fixed;
    bottom: 0px;
    right: 8px;
    font-size: 32px;
    cursor: pointer;
    line-height: normal;
    opacity: 0.5;
    color: #a01eef;
}

.go_to_end:hover {
    color: black;
}