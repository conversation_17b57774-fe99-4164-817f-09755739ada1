
.opr-search {
    width: 180px;
}

.book-block {
    margin: 16px;
    border: 1px solid #eeeeee;
    border-radius: 2px;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: #f3f3f3;

    /*background-color: #fff;*/
    /*border: 1px solid #ccc;*/
    /*padding: 4px;*/
    /*box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);*/
    transition: transform 0.2s;
}

.book-block:hover {
    border: 1px solid #b3b3b3;
    transform: scale(1.05); /* 鼠标悬停时放大效果 */
}

.book-block:hover .title-block {
    color: #ea2a9e;
}


.book-img-block {
    /*max-width: 300px;*/
    width: 165px;
    height: 180px;
    /* 包含，全包 */
    object-fit: cover;
    transition: all 0.5s ease;
}

.book-block--title {
    /*文本不换行*/
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    max-width: 150px;
    font-weight: 500;

    height: 35px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    word-break: break-all;
    -webkit-box-orient: vertical;
}


.genre-and-tags-block {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #eaeaea
}

.book-genre-block {
    max-width: 80px;
}


.my_book_genre_btn {
    /*top: -5px !important;*/
}

.my_book_genre_btn .el-button {
    background-color: #E91E63;
    border: none;
}

.my_book_genre_btn .el-button--mini {
    font-size: 10px !important;
    padding: 4px;
}

.my_book_genre_btn .el-icon-arrow-down {
    font-size: 10px !important;
}


.book-tags-block {
    overflow-x: auto;
    white-space: nowrap;
    display: flex;
    justify-content: flex-start;
    font-size: 12px;
    position: relative;
    max-width: 80px;
}

.book-tags-block i {
    position: absolute;
    right: 4px;
    top: 4px;
    visibility: hidden;
    cursor: pointer;
    color: #007bff;
}

.book-tags-block:hover i {
    visibility: unset;
}

.subject_book_genre_block {
    max-width: 80px;
    font-size: 12px;
}

.subject_book_genre_block a {
    color: #E91E63;
}

.main-book-info {
    padding: 2px;
}


.subject-block {
    margin: 8px;
    border: 1px solid #eeeeee;
    border-radius: 2px;

    position: relative;
    transition: transform 0.2s;
}

.subject-block:hover {
    border: 1px solid #d0d0d0;
    transform: scale(1.05); /* 鼠标悬停时放大效果 */
}

.subject-block:hover a {
    text-decoration: underline;
}

.subject-block img {
    min-width: 150px;
    max-width: 240px;
    height: 160px;
    /* 包含，全包 */
    object-fit: cover;
    transition: all 0.5s ease;
}


.subject-block--title {
    font-weight: 600;
}

.subject-block--title a {
    color: black;
}

.subject-block-detail img {
    max-width: 200px;
    max-height: 240px;
    /*object-fit: contain;*/
}

/* 专题-表格显示 */
.subject-block-for-table {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.subject-block-for-table--book-block {
    width: 18%;
    border: 1px dotted gray;
    margin: 8px 0;
    display: flex;
    z-index: 999;
}


.subject-block-for-table--book-block:hover {
    border: 1px solid gray;
}

.subject-block-for-table--book-block img {
    min-width: 80px;
    width: 80px;
    height: 80px;
    object-fit: contain;
    border: 1px dotted gray;
}

.subject-block-for-table--book-block-title {
    width: 100%;
    color: #0b2e13;
    margin-left: 4px;

    font-size: 12px;
    font-weight: 500;

    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    word-break: break-all;
    -webkit-box-orient: vertical;
}

.same-subject-box {
    display: flex;
    flex-wrap: wrap;
}

.same-subject-box__book {
    display: inline-block;
    border: 1px solid #cdcdcd;
    margin: 4px;
    padding: 4px 8px;
    color: black;
    border-radius: 4px;
    font-size: 12px;
}

.same-subject-box__book__active {
    background-color: var(--primary-color) !important;
    color: white !important;
}

.same-subject-box__book__active span {
    color: white;
}


.my_show_format label {
    margin-bottom: 0;
}


.subject-link-book {
    margin: 16px;
    border: 1px solid #eeeeee;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.subject-link-book:hover {
    border: 1px solid black;
}

.subject-link-book img {
    width: 100px;
    height: 120px;
}