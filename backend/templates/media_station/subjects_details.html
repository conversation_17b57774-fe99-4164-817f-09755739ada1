{% extends '_link.html' %}

{% load static %}


{% block sc %}
    <!-- lozad 图片懒加载 js -->
    <script type="text/javascript" src="{% static 'js/lozad.min.js' %}"></script>

    <!-- 拖动排序 -->
    <script type="text/javascript" src="{% static 'js/sortable.min.js' %}"></script>
{% endblock %}

{% block content %}
    <style>
        .main_box {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
        }


        .views-block {
            position: absolute;
            right: 2px;
            top: 24px;
            font-size: 12px;
            opacity: 0.7;
        }

        .subject-del-btn {
            color: #ff3333;
            cursor: pointer;
            font-size: 14px;
        }


        .book_block_in_subject {
            margin: 8px !important;
        }

        .book_block_in_subject .book-block--title {
            {#max-width: unset;#}
        }

        .book_in_subject {
            min-width: 150px;
            max-width: 300px;
            width: unset !important;
        }

    </style>

    <style>
        .opr_btn_line {
            position: fixed;
            right: 4px;
            top: 70px;
            z-index: 999;
        }

        .book_for_chose {
            border: 1px solid #d4d4d4;
            border-radius: 2px;
            margin: 4px 8px;
            padding: 4px;
            display: flex;
            align-items: center;
        }

        .book_for_chose a {
            margin-left: 4px;
            display: flex;
        }

        .book_for_chose__img {
            max-width: 40px;
            max-height: 30px;
            /* 包含，全包 */
            object-fit: cover;
        }
    </style>

    <div id="subject_details">
        <div class="opr_btn_line">
            <el-radio-group v-model="showFormat" size="mini" class="my_show_format">
                <el-radio-button label="grid">
                    <i class="el-icon-s-grid"></i>
                </el-radio-button>
                <el-radio-button label="table">
                    <i class="el-icon-s-unfold"></i>
                </el-radio-button>
            </el-radio-group>

            <el-radio-group v-model="isEdit" size="mini" style="margin-bottom: -8px">
                <el-radio-button :label="false">阅读模式</el-radio-button>
                <el-radio-button :label="true">编辑模式</el-radio-button>
            </el-radio-group>

            <el-button size="mini" type="primary"
                       @click="readyToBatchAdd">批量添加
            </el-button>

            <!-- 排序 -->
            <el-button v-if="!dragMode"
                       size="mini" type="primary"
                       @click="startSorting">排序
            </el-button>
            <el-button-group v-if="dragMode">
                <el-button size="mini" type="" @click="cancelSorting">取消</el-button>
                <el-button size="mini" type="primary" @click="submitSorting">确定排序</el-button>
            </el-button-group>
        </div>

        <div class="subject-block-detail">
            <div class="subject-block--title">{{ subject.title }}</div>
            <img src="{{ subject.get_thumb_url }}" alt="">
            <div>
                <span class="my_small_text">排序权重：</span>
                <span>{{ subject.asc_priority }}</span>
            </div>
            <div class="my_small_text">{{ subject.created_at }}</div>
        </div>

        <hr>


        <!-- 专题下的所有 图册 -->
        {# ———— 格子显示 ———— #}
        <div v-show="showFormat==='grid'" class="main_box" id="drag_book">

            <div class="book-block book_block_in_subject" v-for="book_item in li_subject_book" :key="book_item.id"
                 :id="book_item.id" :title="book_item.title">
                <a :href="`/book/${book_item.id}`" target="_blank"
                   style="color: #0b2e13;overflow: hidden">
                    {# 图片#}
                    <img class="lozad book-img-block book_in_subject"
                         :data-src="book_item.first_img"
                         src="{% static 'img/blank.png' %}"
                         alt=""
                         @load="handleImageLoad">
                </a>

                <div class="main-book-info">
                    {# 标题区#}
                    <a :href="`/book/${book_item.id}`" target="_blank"
                       style="color: #0b2e13;overflow: hidden">
                        <div class="book-block--title">{[ book_item.title ]} ({[ book_item.page_count ]})</div>
                    </a>

                    {# 浏览量#}
                    <div class="views-block">
                        <i class="el-icon-thumb"></i>
                        {[ book_item.views ]}
                    </div>


                    <!-- 类型与标签 -->
                    <div class="genre-and-tags-block">
                        <div class="subject_book_genre_block">
                            <a :href="`/?genre=${book_item.genre}`" target="_blank">
                                {[ book_item.genre ]}
                            </a>
                        </div>

                        <div class="book-tags-block">
                            <a v-for="tag_item in book_item.tags"
                               :href="`/?tag=${tag_item.value}`" target="_blank">
                                <span class="badge badge-pill badge-success">{[ tag_item.value ]}</span>
                            </a><span>&nbsp;</span>
                        </div>
                    </div>

                    <!-- 编辑模式 才显示 -->
                    <div v-if="isEdit"
                         style="padding: 4px;border-top: 1px solid #cdcdcd;margin-top: 4px;display: flex;align-items: center;justify-content: space-between">
                        <div @click="stopPropagation">
                            <i class="el-icon-delete subject-del-btn" @click="bookDel(book_item.id)"></i>
                        </div>

                        <div style="margin-top: 4px">
                            <el-input-number v-model="book_item.asc_priority" size="mini"
                                             @change="updateBookPriority(book_item.id,book_item.asc_priority)"></el-input-number>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        {# ———— 表格显示 ———— #}
        <div v-show="showFormat==='table'" class="subject-block-for-table">
            <div v-for="book_item in li_subject_book" :key="book_item.id"
                 class="subject-block-for-table--book-block">
                {# 图片#}
                <img class="lozad"
                     :data-src="book_item.first_img"
                     src="{% static 'img/blank.png' %}"
                     alt=""
                     @load="handleImageLoad">

                {# 标题区#}
                <a :href="`/book/${book_item.id}`" target="_blank">
                    <div class="subject-block-for-table--book-block-title">
                        {[ book_item.title ]} ({[ book_item.page_count ]})
                    </div>
                </a>
            </div>

        </div>

        <!-- 弹出框-批量添加 -->
        <el-dialog
                :title="`批量添加 ${booksForChose.count}`"
                :visible.sync="addDialogVisible"
                width="80%"
                :close-on-click-modal="false"
                @close="handleClose">
            <div>
                <el-input v-model="bookForChoseForm.key" size="mini" class="opr-search"
                          placeholder="搜索关键词"></el-input>

                <el-select v-model="bookForChoseForm.genre" size="mini" placeholder="选择 genres" clearable>
                    {% for genre_item in li_genres %}
                        <el-option label="{{ genre_item.value }}"
                                   value="{{ genre_item.value }}">
                            <div style="display: flex;justify-content: space-between">
                                <span>{{ genre_item.value }}</span>
                                <div>
                                    <span class="badge badge-pill badge-secondary">{{ genre_item.get_book_count }}</span>
                                </div>
                            </div>
                        </el-option>
                    {% endfor %}
                </el-select>

                <el-select v-model="bookForChoseForm.li_tags" size="mini" placeholder="选择 tags" multiple clearable>
                    {% for tag_item in li_tags %}
                        <el-option value="{{ tag_item.value }}">
                            <div style="display: flex;justify-content: space-between">
                                <span>{{ tag_item.value }}</span>
                                <div>
                                    <span class="badge badge-pill badge-secondary">{{ tag_item.get_book_count }}</span>
                                </div>
                            </div>
                        </el-option>
                    {% endfor %}
                </el-select>

                <el-button size="mini" @click="resetBookForChose" type="info" plain>重置</el-button>
            </div>

            <el-divider></el-divider>

            <el-checkbox v-model="hasChoseAll" @change="choseAllLine">全选</el-checkbox>

            <div style="display: flex;flex-wrap: wrap">
                <div v-for="book_item in booksForChose.list" class="book_for_chose">
                    <el-checkbox :value="checkIsChosen(book_item.id)"
                                 @change="toggleChose($event,book_item.id)">
                    </el-checkbox>
                    <a :href=`/book/${book_item.id}` target="_blank" style="color: #0b2e13;">
                        <img class="book_for_chose__img" :src="`${book_item.first_img}`"
                             alt="">

                        <div>{[ book_item.title ]} ({[ book_item.page_count ]})</div>
                    </a>
                </div>
            </div>

            <el-divider></el-divider>

            <div style="margin-top: 8px">
                <el-pagination
                        small
                        background
                        layout="total, prev, pager, next, sizes"
                        :current-page="bookForChoseForm.page"
                        :total="booksForChose.count"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="bookForChoseForm.size"
                        @current-change="changePage"
                        @size-change="changeSize">
                </el-pagination>
            </div>


            <div slot="footer" class="dialog-footer">
                <el-button @click="handleClose">取 消</el-button>
                <el-button type="primary" @click="startBatchAdd">确 定</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        const subjectId = {{ subject.id | safe }};

        var vm = new Vue({
            delimiters: ["{[", "]}"],   // 将 vue 的分隔符重定义为 {[
            el: "#subject_details",
            data: {
                li_subject_book: [],    // 专题下 所有 图册

                addDialogVisible: false,

                isEdit: false,    // false-阅读模式，true-编辑模式

                // 选中的 book id
                liBookIsChosen: [],
                hasChoseAll: false, // 是否全选的标志

                // 选择-图册-表单
                bookForChoseForm: {
                    key: '',
                    page: 1,
                    size: 10,
                    genre: '',
                    li_tags: [],
                    ignore_by_subject_id: subjectId,
                    only_subject: -1
                },
                // 选择-图册-数据
                booksForChose: {
                    list: [],
                    count: 0
                },

                // 图片懒加载对象在 vue的对象
                observer: null,

                showFormat: 'grid', // 0-grid 格子， 1-table 表格

                dragMode: false,   // 拖动排序模式
            },
            methods: {
                /**
                 * 加载 专题-所有图册-列表
                 */
                loadAllSubjectBooksList() {
                    $.ajax({
                        type: 'GET',
                        url: `/aj_subject/${subjectId}/book/list/`,
                        success: (data) => {
                            if (data.code === 0) {
                                this.li_subject_book = data.data;
                            }
                        },
                        error: function () {
                        }
                    })
                },
                /**
                 * 修改 图册-排序权重
                 */
                updateBookPriority(bookId, priority) {
                    console.log(bookId, priority)
                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/${subjectId}/book/${bookId}/asc_priority/update/`,
                        data: {
                            asc_priority: priority
                        },
                        success: (data) => {
                            this.loadAllSubjectBooksList()

                            this.$message.success('修改成功')
                        },
                        error: function () {
                        }
                    })
                },
                handleClose() {
                    this.addDialogVisible = false
                },
                readyToBatchAdd() {
                    this.loadBookForChoseList()
                    this.addDialogVisible = true
                },
                loadBookForChoseList() {
                    $.ajax({
                        type: 'GET',
                        url: '/aj_book/list/',
                        data: this.bookForChoseForm,
                        success: (data) => {
                            if (data.code === 0) {
                                this.booksForChose = data.data;
                            }
                        },
                        error: function () {
                        }
                    })
                },
                changePage(page) {
                    this.bookForChoseForm.page = page
                },
                changeSize(size) {
                    this.bookForChoseForm.size = size
                },
                /*
                判断 是否选中
                 */
                checkIsChosen(bookId) {
                    return this.liBookIsChosen.some(item => item === bookId)
                },
                /**
                 * 处理 选中点击
                 * @param val
                 * @param bookId
                 */
                toggleChose(val, bookId) {
                    if (val) {    // 选中：添加
                        this.liBookIsChosen.push(parseInt(bookId))
                        return
                    }
                    console.log('————')
                    // 取消选择：删除
                    this.liBookIsChosen = this.liBookIsChosen.filter((item) => item !== bookId)
                },
                /**
                 * 全选 处理
                 */
                choseAllLine() {
                    if (this.hasChoseAll) {
                        // 准备全选
                        this.liBookIsChosen = this.booksForChose.list.map(item => item.id)
                    }
                },
                /**
                 * 批量-添加图册
                 */
                startBatchAdd() {
                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/${subjectId}/book/batch_add/`,
                        data: {
                            li_book_id: this.liBookIsChosen
                        },
                        success: (data) => {
                            // 刷新 专题-图册=列表
                            this.loadAllSubjectBooksList()

                            // 关闭 弹出框
                            this.addDialogVisible = false

                            // 重置待选中列表
                            this.resetBookForChose()

                            this.$message.success('批量添加成功')
                        },
                        error: function () {
                        }
                    })
                },
                /**
                 * 切换 模式
                 */
                toggleMode() {
                    this.isEdit = !this.isEdit
                },
                /**
                 * 修改 图册-权重
                 * @param bookId
                 * @param bookPriority
                 */
                updateBookAscPriority(bookId, bookPriority) {
                    console.log(bookId, bookPriority)
                },
                /**
                 * 阻止 冒泡
                 * @param e
                 */
                stopPropagation(e) {
                    e.stopPropagation()
                },
                /**
                 * 图册-删除
                 * @param bookId
                 */
                bookDel(bookId) {
                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/${subjectId}/book/${bookId}/del/`,
                        success: (data) => {
                            if (data.code === 0) {
                                this.loadAllSubjectBooksList()
                                this.$message.error('删除成功')
                            }
                        },
                        error: function () {
                        }
                    })
                },
                /**
                 * 重置 待选中列表
                 */
                resetBookForChose() {
                    // 重置 全选标志
                    this.hasChoseAll = false

                    // 清空已选中
                    this.liBookIsChosen = []

                    // 重新请求 待选中列表
                    this.bookForChoseForm = {
                        key: '',
                        page: 1,
                        size: 10,
                        genre: '',
                        li_tags: [],
                        ignore_by_subject_id: subjectId,
                        only_subject: -1
                    }
                },
                handleImageLoad(event) {
                    // 当图片加载完成时,手动触发 lozad 观察器
                    this.observer.observe(event.target)
                },
                /**
                 * 开始 拖动排序
                 */
                startSorting() {
                    new Sortable(drag_book, {
                        animation: 150,
                        forceFallback: false,
                    })
                    this.dragMode = true
                },
                /**
                 * 取消 拖动排序
                 */
                cancelSorting() {
                    location.reload()
                },
                /**
                 * 提交 拖动排序
                 */
                submitSorting() {
                    console.log('submit sorting...')
                    let newSortingDom = []
                    $('.book-block').each((index, item) => {
                        newSortingDom.push(
                            $(item).attr('id')  // book id
                        )
                    })

                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/${subjectId}/book/all_sort/`,
                        data: {
                            li_book_id_with_new_sort: newSortingDom
                        },
                        success: (data) => {
                            if (data.code === 0) {
                                this.$message.success('排序成功')

                                setTimeout(() => {
                                    location.reload()
                                }, 1000)
                            }
                        },
                        error: function () {
                        }
                    })
                }
            },
            watch: {
                bookForChoseForm: {
                    deep: true,
                    handler: function () {
                        this.loadBookForChoseList()
                    }
                }
            },
            mounted() {
                // 初始化 lozad 观察器
                this.observer = lozad()
                this.observer.observe()

                // 加载 专题的 所有图册
                this.loadAllSubjectBooksList()
            }
        })
    </script>

{% endblock content %}

