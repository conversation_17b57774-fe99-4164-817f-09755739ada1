{% extends '_link.html' %}
{% load static %}


{% block sc %}
    <!-- 拖动排序 -->
    <script type="text/javascript" src="{% static 'js/sortable.min.js' %}"></script>
{% endblock %}

{% block content %}
    <style>
        .main_box {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
        }


        #subject_list {
            position: relative;
            margin-top: 32px;
        }

        .right_corner_btn {
            position: fixed;
            right: 4px;
            top: 70px;
        }
    </style>

    <div id="subject_list">
        <div class="right_corner_btn">
            <el-button size="mini" type="primary"
                       @click="goSubjectAdd">添加
            </el-button>

            <el-button size="mini" type="primary"
                       @click="toggleMode">编辑模式
            </el-button>

            <!-- 排序 -->
            <el-button v-if="!dragMode"
                       size="mini" type="primary"
                       @click="startSorting">排序
            </el-button>
            <el-button-group v-if="dragMode">
                <el-button size="mini" type="" @click="cancelSorting">取消</el-button>
                <el-button size="mini" type="primary" @click="submitSubjectSorting">确定排序</el-button>
            </el-button-group>
        </div>

        <div class="main_box" id="drag_subject">
            <div class="subject-block" v-for="subject in subjects.list" :id="subject.id">
                <div>
                    <a :href="`/subjects/${subject.id}/`">
                        <img :src="subject.thumb_url" alt="">
                    </a>
                </div>
                <div style="display: flex;align-items: center">
                    <div class="subject-block--title">
                        <a :href="`/subjects/${subject.id}/`">
                            {[ subject.title ]}
                        </a>
                    </div>
                    <div class="my_small_text">
                        ({[ subject.book_count ]})
                    </div>
                </div>
                <!-- 编辑模式 才显示 -->
                <div v-if="isEdit" @click="stopPropagation">
                    <el-input-number v-model="subject.asc_priority" size="mini"
                                     @change="updateAscPriority(subject.id,subject.asc_priority)"></el-input-number>
                </div>
            </div>
        </div>

    </div>


    <script>
        new Vue({
            delimiters: ["{[", "]}"],   // 将 vue 的分隔符重定义为 {[
            el: "#subject_list",
            data: {
                loading: false,
                isEdit: false,    // false-阅读模式，true-编辑模式

                form: {
                    key: '',
                    page: 1,
                    size: 20,
                },

                subjects: {
                    list: [],
                    count: 0
                },

                dragMode: false,   // 拖动排序模式
            },
            methods: {
                goSubjectAdd() {
                    window.location.href = '/subjects/add/'
                },
                loadData() {
                    this.loading = true
                    $.ajax({
                        type: 'GET',
                        url: '/aj_subject/list/',
                        data: this.form,
                        success: (data) => {
                            if (data.code === 0) {
                                this.subjects.list = data.data.list;
                                this.subjects.count = data.data.count;
                            } else {
                                alert('Error: ' + data.error_message);
                            }
                            this.loading = false
                        },
                        error: function () {
                            alert('Error');
                            this.loading = false
                        }
                    })
                },
                toggleMode() {
                    this.isEdit = !this.isEdit
                },
                updateAscPriority(subjectId, val) {
                    this.loading = true
                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/${subjectId}/asc_priority/update/`,
                        data: {
                            asc_priority: val
                        },
                        success: (data) => {
                            this.loadData()
                            this.$message.success('修改成功');
                        },
                        error: function () {
                            this.loading = false
                        }
                    })
                },
                stopPropagation(e) {
                    e.stopPropagation()
                },
                /**
                 * 开始 拖动排序
                 */
                startSorting() {
                    new Sortable(drag_subject, {
                        animation: 150,
                        forceFallback: false,
                    })
                    this.dragMode = true
                },
                /**
                 * 取消 拖动排序
                 */
                cancelSorting() {
                    location.reload()
                },
                /**
                 * 提交 拖动排序
                 */
                submitSubjectSorting() {
                    console.log('submit sorting...')
                    let liNewSortingId = []
                    $('.subject-block').each((index, item) => {
                        liNewSortingId.push(
                            $(item).attr('id')  // book id
                        )
                    })

                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/all_sort/`,
                        data: {
                            li_subject_id_with_new_sort: liNewSortingId
                        },
                        success: (data) => {
                            if (data.code === 0) {
                                this.$message.success('排序成功')

                                setTimeout(() => {
                                    location.reload()
                                }, 1000)
                            }
                        },
                        error: function () {
                        }
                    })
                }
            },
            mounted() {
                this.loadData()
            }
        })
    </script>

{% endblock content %}

