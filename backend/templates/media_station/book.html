{% extends '_link.html' %}

{% load static %}

{% block sc %}
    <!-- lozad 图片懒加载 js -->
    <script type="text/javascript" src="{% static 'js/lozad.min.js' %}"></script>

    <!-- viewer 图片预览 css -->
    <link rel="stylesheet" href="{% static 'css/viewer.min.css' %}">

    <!-- viewer 图片预览 js -->
    <script type="text/javascript" src="{% static 'js/viewer.min.js' %}"></script>
{% endblock %}

{% block content %}
    <style>
        .title-line {
            display: flex;
            justify-content: space-between;
        }

        .go-back {
            cursor: pointer;
            color: #3a8ee6;
        }

        .book-page-box {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            padding-top: 8px;
        }


        .img-block {
            margin: 8px;
            border: 1px solid #d0d0d0;
            border-radius: 2px;

            height: 200px;
            cursor: pointer;
        }

        .img-block:hover {
            border: 1px solid #797979;
        }

        {#竖屏#}
        .book-page-box-vertical {
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            padding-top: 8px;
        }

        .img-block-vertical {
            width: 100%;
            margin: 0 auto;
            border: 1px solid #d0d0d0;
            cursor: pointer;
        }

        .img-block-vertical:hover {
            border: 1px solid #797979;
        }

        .opr-block {
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #d0d0d0;
        }

        .opr-block div {
            margin-right: 20px;
        }

        .slide-block {
            width: 300px;
        }

        .base-info {
            position: relative;
            color: gray;
        }

        .collect-block {
            position: absolute;
            right: 2px;
            top: 0;
        }

        .del-block {
            position: absolute;
            right: 2px;
            top: 80px;
        }

        .del-block i {
            font-size: 32px;
            color: #ff0b0b;
            cursor: pointer;
        }

        .collect-item {
            font-size: 32px;
            color: #f15806;
            cursor: pointer;
        }

        .collect-item:hover {
            color: #f57e3a;
        }

        .recommend_block {
            margin-bottom: 40px;
        }

        .recommend_btn {
            position: fixed;
            bottom: 100px;
            right: 16px;
            cursor: pointer;
        }

        .recommend_btn i {
            color: #007bff;
        }


        .list-group-item {
            padding: unset;
        }

        .recommend_img {
            height: 40px;
            width: 40px;
            object-fit: contain;
            border: 1px solid #eaeaea;
            margin-right: 8px;
        }

        .recommend_item {
            padding: 4px;
            display: flex;
            align-items: center;
            cursor: pointer;
            width: 100%;
            color: black;
        }

        .recommend_item:hover {
            text-decoration: underline;
        }

        .recommend_item__active {
            background-color: #3982ff;
            color: white;
        }

        /*小于 移动设备标准*/
        @media screen and (max-width: 768px) {
            .main-box {
                margin: 0;
            }

            .book-page-box-vertical {
                width: auto;
            }

            .img-block {
                margin: 0;
                object-fit: contain;
                max-width: 100%;
            }


            .el-dialog {
                width: 80%;
            }

            /* 移动端覆盖 抽屉默认宽度 */
            .el-drawer {
                width: 60% !important;
            }
        }


        .prev_page_buffer {
            display: flex;
            flex-direction: column-reverse;
            justify-content: flex-end;

            width: 100%;
            height: 120px;
            background-color: #f5f5f5;
            color: #999;
            padding-bottom: 16px;
            border-radius: 4px;
            text-align: center;
            margin-top: -16px;
        }

        .next_page_buffer {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;

            width: 100%;
            height: 180px;
            background-color: #f5f5f5;
            color: #999;
            margin: 20px 0;
            padding-top: 16px;
            border-radius: 4px;
            text-align: center;
        }

    </style>

    <div id="app_book">
        <!-- 添加上一册缓冲区到顶部 -->
        {% if pre_link %}
            <div class="prev_page_buffer">
                <div>
                    <div>↑</div>
                    <div>↑</div>
                    <div>↑</div>
                    <div>↑</div>
                    <div>上一册</div>
                </div>
            </div>
        {% endif %}

        <div>
            <div style="margin-bottom: 8px;">
                <div class="title-line">
                    <h3>{[ book.title ]}</h3>
                </div>
                <div class="base-info">
                    <div>Pages: {[ book.page_count ]}</div>
                    <div class="my_flex_y_center">
                        Genre:
                        <div style="margin-left: 4px;color: #E91E63" id="genre_box">{[ book.genre ]}</div>
                        <el-button type="text" icon="el-icon-edit" size="mini" style="margin-left: 16px"
                                   @click="setGenre.visible=true"></el-button>

                        <el-dialog
                                title="修改类型"
                                :visible.sync="setGenre.visible">
                            <div>
                                <el-select v-model="setGenre.value" size="mini" placeholder="选择 genres" clearable
                                           style="width: 100%">
                                    {% for genre_item in li_genres %}
                                        <el-option label="{{ genre_item.value }}"
                                                   value="{{ genre_item.value }}">
                                            <div style="display: flex;justify-content: space-between">
                                                <span>{{ genre_item.value }}</span>
                                                <div>
                                                    <span class="badge badge-pill badge-secondary">{{ genre_item.get_book_count }}</span>
                                                </div>
                                            </div>
                                        </el-option>
                                    {% endfor %}
                                </el-select>
                            </div>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="setGenre.visible = false" size="mini">取 消</el-button>
                                <el-button type="primary" @click="goSetGenre" size="mini">确 定</el-button>
                            </div>
                        </el-dialog>
                    </div>
                    <div class="my_flex_y_center">Tags:
                        <div class="tags-block" style="margin-left: 4px">
                            <a v-for="tag in book.tags" :href="`/?tag=${ tag.value }`">
                                <span class="badge badge-pill badge-success book_tag_item">{[ tag.value ]}</span>
                            </a>
                        </div>
                        <el-button type="text" icon="el-icon-edit" size="mini" style="margin-left: 16px"
                                   @click="bindTag.visible=true"></el-button>

                        <el-dialog
                                title="修改标签"
                                :visible.sync="bindTag.visible">
                            <div>
                                <el-checkbox-group v-model="bindTag.liTags" size="mini">
                                    {% for tag_item in li_tags %}
                                        <el-checkbox size="mini" label="{{ tag_item.value }}" border></el-checkbox>
                                    {% endfor %}
                                </el-checkbox-group>
                            </div>
                            <div slot="footer" class="dialog-footer">
                                <el-button @click="bindTag.visible = false" size="mini">取 消</el-button>
                                <el-button type="primary" @click="goUpdateTags" size="mini">确 定
                                </el-button>
                            </div>
                        </el-dialog>
                    </div>
                    <div>Views: {[ book.views ]}</div>
                    <div>Created at: <span class="my_small_text">{[ book.created_at ]}</span></div>
                    <div>Updated at: <span class="my_small_text">{[ book.updated_at ]}</span></div>

                    {# 收藏#}
                    <div class="collect-block">
                        <i class="collect-item"
                           :class="`el-icon-star-${book.is_collect?'on':'off'}`"
                           @click="collect"></i>
                    </div>

                    {# 删除#}
                    <div class="del-block">

                        <el-popover
                                placement="top"
                                width="160"
                                v-model="delConfirmVisible">
                            <h6>即将删除整本书，是否继续？</h6>
                            <div style="text-align: right; margin: 0">
                                <el-button size="mini" type="text" @click="delConfirmVisible = false">取消</el-button>
                                <el-button type="danger" size="mini" @click="delBook">确定</el-button>
                            </div>

                            <i class="el-icon-delete" slot="reference"></i>
                        </el-popover>

                    </div>
                </div>
            </div>

            {#操作栏#}
            <div class="opr-block">
                <div style="min-width: 135px">
                    <el-radio-group v-model="imgDirection" size="mini">
                        <el-radio-button :label="0">横屏</el-radio-button>
                        <el-radio-button :label="1">竖屏</el-radio-button>
                    </el-radio-group>
                </div>

                {#                宽/高#}
                <div class="slide-block">
                    <el-slider v-if="isMobile" v-model="imgLengthMobile[imgDirection]"></el-slider>
                    <el-slider v-else v-model="imgLength[imgDirection]"></el-slider>
                </div>
            </div>

            {# 遍历每一张图片#}
            <div :class="imgDirection===0?'book-page-box':'book-page-box-vertical'" id="viewer">
                {% for image_item in li_image_page %}
                    {#图片#}
                    <img data-src="{{ image_item.img_path.url }}"
                         src="{% static 'img/blank.png' %}"
                         alt="page:{{ image_item.page }} order:{{ image_item.order }}"
                         class="lozad"
                         :class="imgDirection===0?'img-block':'img-block-vertical'"
                         :style="imgLengthStl">
                {% endfor %}

            </div>

            {% if pre_link or next_link %}
                <hr>
                <div class="my_flex_space" style="width: 100%;padding:0 16px 8px;">
                    <div>
                        {% if pre_link %}
                            <a href="{{ pre_link }}">上一册</a>
                        {% endif %}
                    </div>
                    <div>
                        {% if next_link %}
                            <a href="{{ next_link }}">下一册</a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            <hr>

            <!-- 其它的专题图册 -->
            <div>
                {% for subject in li_subject_book %}
                    <div>
                        <div class="subject-block--title" style="max-width: unset">
                            <span class="my_small_text">专题：</span>
                            <a href="/subjects/{{ subject.id }}/">{{ subject.title }}</a>
                        </div>

                        <div class="same-subject-box">
                            {% for same_subject_book in subject.li_book %}
                                <a href="/book/{{ same_subject_book.id }}/"
                                   class="same-subject-box__book {% if same_subject_book.id == book_id %} same-subject-box__book__active {% endif %}">
                                    <el-popover
                                            placement="top"
                                            trigger="hover">
                                        <div>
                                            <img data-src="{{ same_subject_book.thumb_url }}"
                                                 src="{% static 'img/blank.png' %}"
                                                 class="lozad"
                                                 style="max-width: 300px;max-height: 300px"
                                                 alt="">
                                        </div>
                                        <img slot="reference"
                                             style="width: 24px;height: 24px;object-fit: contain;margin-right: 4px"
                                             data-src="{{ same_subject_book.thumb_url }}"
                                             src="{% static 'img/blank.png' %}"
                                             class="lozad"
                                             alt="">
                                    </el-popover>
                                    <span class="my_small_text">{{ same_subject_book.asc_priority }} </span>
                                    <span>{{ same_subject_book.title }}</span>
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                    <hr>
                {% endfor %}
            </div>

        </div>

        <div class="recommend_btn">
            <i class="el-icon-connection" @click="openRecommend"></i>
        </div>

        <el-drawer
                title="相关/推荐"
                :visible.sync="recommendVisible"
                direction="rtl">
            <div>
                {#相关#}
                <ul class="list-group recommend_block">
                    <h5 style="margin-left: 4px">相关</h5>
                    <template v-for="recommend_book in liRecommendBooks">
                        <li class="list-group-item">
                            <a :href="`/book/${recommend_book.id}`"
                               :class="`recommend_item ${recommend_book.id === book.id?'recommend_item__active':''}`">
                                <el-popover
                                        placement="left"
                                        trigger="hover">
                                    <div>
                                        <img style="max-width: 300px;max-height: 300px"
                                             :src="`${recommend_book.first_img}`" alt="">
                                    </div>
                                    <img slot="reference"
                                         :src="`${recommend_book.first_img}`" alt=""
                                         class="recommend_img">
                                </el-popover>
                                <div>{[ recommend_book.title ]} ({[ recommend_book.page_count ]})</div>
                            </a>
                        </li>
                    </template>
                </ul>

                {#推荐#}
            </div>
        </el-drawer>
    </div>

    <script>
        const bookId = {{ book_id }};

        var vm = new Vue({
            delimiters: ["{[", "]}"],   // 将 vue 的分隔符重定义为 {[
            el: "#app_book",
            data: {
                delConfirmVisible: false,

                // 书本详情信息
                book: {
                    id: bookId
                },

                imgDirection: 0,   // 0-横屏 1-竖屏
                imgLength: {
                    0: 20,
                    1: 60
                },

                // 移动端
                imgLengthMobile: {
                    0: 60,  // 横屏默认 滑动初始值 60%
                    1: 100   // 竖屏默认不需要宽度，直接 width:100%
                },
                mobileMaxWidth: 768,

                // 修改类型
                setGenre: {
                    visible: false,
                    value: ''
                },

                // 修改标签
                bindTag: {
                    visible: false,
                    liTags: [],     // 已选中的标签 字符串-列表
                },

                // 相关推荐
                recommendVisible: false,
                liRecommendBooks: []
            },
            methods: {
                collect() {
                    $.ajax({
                        type: 'POST',
                        url: '/aj_collect/',
                        data: {id: bookId},
                        success: (data) => {
                            if (data.code === 0) {
                                if (data.data === 1) {  // 已收藏
                                    $('.collect-item').removeClass('el-icon-star-off');
                                    $('.collect-item').addClass('el-icon-star-on');
                                } else {    // 未收藏
                                    $('.collect-item').removeClass('el-icon-star-on');
                                    $('.collect-item').addClass('el-icon-star-off');
                                }
                                this.$message.success('修改成功');
                            } else {
                                alert('Error: ' + data.msg);
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                delBook() {
                    location.href = `/book/${this.book.id}/del`
                },
                goUpdateTags() {
                    let data = {
                        li_books_id: [bookId],
                        li_tags: this.bindTag.liTags
                    }
                    {#console.log(data)#}
                    $.ajax({
                        type: 'POST',
                        url: '/aj_batch_update_tags/',
                        data,
                        success: (data) => {
                            this.bindTag.visible = false;
                            if (data.code === 0) {
                                this.$message.success('标签绑定成功')
                                this.loadData()
                            } else {
                                alert('Error: ' + data.msg);
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                goSetGenre() {
                    $.ajax({
                        type: 'POST',
                        url: '/aj_batch_set_genre/',
                        data: {
                            li_books_id: [bookId],
                            genre: this.setGenre.value
                        },
                        success: (data) => {
                            this.setGenre.visible = false;
                            if (data.code === 0) {
                                this.$message.success('绑定成功')

                                // 成功，直接设置 选中的 value即可
                                $('#genre_box').text(this.setGenre.value)
                            } else {
                                alert('Error: ' + data.msg);
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                loadData() {
                    $.ajax({
                        type: 'GET',
                        url: '/aj_book/get/',
                        data: {id: bookId},
                        success: (data) => {
                            if (data.code === 0) {
                                // 加载 书本详情
                                this.book = data.data

                                // 更新 已选中标签列表
                                this.bindTag.liTags = this.book.tags.map(item => item.value)
                            } else {
                                alert('Error: ' + data.error_message);
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                openRecommend() {
                    this.recommendVisible = true
                    if (this.liRecommendBooks.length === 0) {
                        this.loadRecommendData()
                    }
                },
                loadRecommendData() {
                    $.ajax({
                        type: 'GET',
                        url: '/aj_recommend_list',
                        data: {book_id: bookId},
                        success: (data) => {
                            if (data.code === 0) {
                                // 更新 已选中标签列表
                                this.liRecommendBooks = data.data.list
                            } else {
                                alert('Error: ' + data.error_message);
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                handleScroll() {
                    // 获取滚动位置
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    // 获取页面总高度
                    const scrollHeight = document.documentElement.scrollHeight;
                    // 获取视窗高度
                    const clientHeight = document.documentElement.clientHeight;

                    // 滚动到顶部时(只有当真正到达顶部时才触发)
                    if (scrollTop === 0) {
                        const preLink = '{{ pre_link }}';
                        if (preLink) {
                            // 添加延时，避免意外触发
                            setTimeout(() => {
                                // 再次确认是否在顶部
                                if (window.pageYOffset === 0) {
                                    window.location.href = preLink;
                                }
                            }, 100);
                        }
                    }

                    // 滚动到底部时(需要真正触底)
                    if (scrollHeight - scrollTop - clientHeight <= 1) {
                        const nextLink = '{{ next_link }}';
                        if (nextLink) {
                            // 添加延时，避免意外触发
                            setTimeout(() => {
                                // 再次确认是否触底
                                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                                const currentScrollHeight = document.documentElement.scrollHeight;
                                const currentClientHeight = document.documentElement.clientHeight;
                                if (currentScrollHeight - currentScrollTop - currentClientHeight <= 1) {
                                    window.location.href = nextLink;
                                }
                            }, 100);
                        }
                    }
                },
                // 添加节流函数
                throttleScroll: function () {
                    let timer = null;
                    return () => {
                        if (timer) return;
                        timer = setTimeout(() => {
                            this.handleScroll();
                            timer = null;
                        }, 200);
                    }
                },
            },
            computed: {
                isMobile() {
                    return document.body.clientWidth < this.mobileMaxWidth;
                },
                imgLengthStl: function () {
                    // ———————— 小屏 ————————
                    if (this.isMobile) {
                        // 横屏
                        if (this.imgDirection === 0) {
                            return {
                                height: this.imgLengthMobile[0] * 2 + "px",  // 横屏 限制 高度
                            };
                        }
                        // 竖屏
                        return {
                            {#width: this.imgLengthMobile[1] * 4 + "px",   // 竖屏 限制宽度#}
                            width: this.imgLengthMobile[1] + '%'
                        };
                    }

                    // ———————— 大屏 ————————
                    // 横屏
                    if (this.imgDirection === 0) {
                        return {
                            height: this.imgLength[0] * 10 + "px",  // 横屏 限制 高度
                        };
                    }
                    // 竖屏
                    return {
                        width: this.imgLength[1] * 10 + "px",   // 竖屏 限制宽度
                    };
                },
            },
            mounted() {
                // 加载 书本信息
                this.loadData()

                // 图片懒加载
                let ob = lozad(); // lazy loads elements with default selector as '.lozad'
                ob.observe();

                // 移动端，进来默认是竖屏
                if (this.isMobile) {
                    this.imgDirection = 1
                }

                // 使用节流函数包装滚动监听
                if ('{{ pre_link }}' || '{{ next_link }}') {
                    this._throttleScroll = this.throttleScroll();
                    window.addEventListener('scroll', this._throttleScroll);
                }
            },
            beforeDestroy() {
                if ('{{ pre_link }}' || '{{ next_link }}') {
                    window.removeEventListener('scroll', this._throttleScroll);
                }
            }
        })
    </script>

    <script>
        // 缩放插件
        new Viewer(document.getElementById('viewer'), {
            title: true,
            rotatable: false,
            scalable: false,
            fullscreen: true,
            zoomRatio: 0.5,
            url: 'data-src'
        });
    </script>

    <script>

        function scrollToTopWithOffset(offset = 200) {
            // 获取视口的高度
            const windowHeight = window.innerHeight;
            // 计算目标滚动位置
            const targetScrollPosition = offset;

            // 使用滚动动画
            window.scrollTo({
                top: targetScrollPosition,
                {#behavior: 'smooth' // 平滑滚动#}
            });
        }

        function scrollToBottomWithOffset(offset = 200) {
            // 获取文档的总高度
            const documentHeight = document.documentElement.scrollHeight;
            // 获取视口的高度
            const windowHeight = window.innerHeight;
            // 计算目标滚动位置
            const targetScrollPosition = documentHeight - windowHeight - offset;

            // 使用滚动动画
            window.scrollTo({
                top: targetScrollPosition,
                {#behavior: 'smooth' // 平滑滚动#}
            });
        }
    </script>


{% endblock content %}


{% block anchorJump %}

    {#返回顶部#}
    <a class="back_to_top">
        <i class="el-icon-caret-top"
           onclick="scrollToTopWithOffset(50)"></i>
    </a>

    {#返回底部#}
    <a class="go_to_end"
       onclick="scrollToBottomWithOffset(50)">
        <i class="el-icon-caret-bottom"></i>
    </a>

{% endblock %}



{% block footer %}
    <!-- 翻页 滚动 缓冲区域 -->
    {% if next_link %}
        <div class="next_page_buffer">
            <div>
                <div>下一册</div>
                <div>↓</div>
                <div>↓</div>
                <div>↓</div>
                <div>↓</div>
                <div>↓</div>
            </div>
        </div>
    {% endif %}
{% endblock %}