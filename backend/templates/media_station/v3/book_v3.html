{% extends "media_station/v3/_base.html" %}
{% load static %}
<!-- 自定义css -->
{% block head %}
  <link rel="stylesheet" href="{% static 'css/v3/book_v3.css' %}" />
  <link rel="stylesheet" href="{% static 'css/v3/book_v3__media.css' %}" />
  <!-- viewer 图片预览 css -->
  <link rel="stylesheet" href="{% static 'css/viewer.min.css' %}">
{% endblock head %}
{% block content %}
  <div class="book-details-container">
    <!-- 基本信息 -->
    <div class="book-base-info-block">
      <div class="book-base-info">
        <div id="bookId">{{ book.id }}</div>
        <div class="book-header">
          <!-- 标题 -->
          <div class="book-title-block">
            <div class="book-base-info--title">{{ book.title }}</div>
            <i class="fa fa-edit edit-book-title-btn" onclick="editTitle(event)"></i>
          </div>
          <!-- 数量 -->
          <div class="my-small-gray-text">
            <i class="fa fa-image"></i>
            {{ book.page_count }}
          </div>
          <!-- 浏览量 -->
          <div class="my-small-gray-text">
            <i class="fa fa-eye"></i>
            {{ book.views }}
          </div>
        </div>
        <!-- 其它信息 -->
        <div class="book-info--other">
          <!-- 类型 -->
          <div class="book-info--other-item genre-line">
            <div>类型:</div>
            <div class="book-genre-value">{{ book.genre|default:"" }}</div>
            <i class="fa fa-edit edit-book-genre-btn" onclick="editGenre(event)"></i>
          </div>
          <!-- 标签 -->
          <div class="book-info--other-item tags-line">
            <div>标签:</div>
            <div class="book-info--other-item--tags">
              {% for tag in book.get_tags %}<div class="book-single-tag">{{ tag.value }}</div>{% endfor %}
            </div>
            <i class="fa fa-edit edit-book-tags-btn" onclick="editTags(event)"></i>
          </div>
          <!-- 专题 -->
          <div class="book-info--other-item subject-line">
            <div>专题:</div>
            <div class="book-info--other-item--subjects">
              {% for subject in li_subject_book %}
                <a class="book-single-subject"
                   href="{% url 'subject_v3_detail' subject.id %}"
                   title="{{ subject.title }}"
                   data-id="{{ subject.id }}">{{ subject.title }}</a>
              {% endfor %}
            </div>
            <i class="fa fa-edit edit-book-subject-btn"
               onclick="editSubjects(event)"></i>
          </div>
          <!-- 备注（有内容才显示） -->
          {% if book.remarks %}
            <div class="book-info--other-item">
              <div>备注:</div>
              <div>{{ book.remarks }}</div>
            </div>
          {% endif %}
          <!-- 时间 -->
          <div class="book-info--other-item">
            <div>创建:</div>
            <div class="my-small-gray-text">{{ book.created_at }}</div>
          </div>
          <div class="book-info--other-item">
            <div>修改:</div>
            <div class="my-small-gray-text">{{ book.updated_at }}</div>
          </div>
          <div class="book-info--other-item">
            <div>方向:</div>
            <div>
              <div class="btn-groups" id="btnGroupsDirection">
                <!-- 按钮组，单选，由 横屏、竖屏 组成 -->
                <button class="btn-normal" data-direction-value="0" id="btnHorizontal">横屏</button>
                <button class="btn-normal" data-direction-value="1" id="btnVertical">竖屏</button>
              </div>
            </div>
          </div>
          <div class="book-info--other-item">
            <div>尺寸:</div>
            <div>
              <!-- 尺寸调节滑块 -->
              <div class="size-slider-container">
                <button class="size-slider-btn" id="decreaseSize">
                  <i class="fa fa-minus"></i>
                </button>
                <input type="range"
                       id="sizeSlider"
                       class="size-slider"
                       min="0"
                       max="100"
                       value="20" />
                <button class="size-slider-btn" id="increaseSize">
                  <i class="fa fa-plus"></i>
                </button>
                <span class="size-slider-value">20</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧 按钮 -->
      <div class="book-base-info--right-btn-block">
        <!-- 收藏按钮 -->
        <div class="book-base-info--collect {% if is_collect %}active{% endif %}">
          <i class="fa fa-star"></i>
        </div>
        <!-- 下载按钮 -->
        <div class="book-base-info--download">
          <i class="fa fa-download"></i>
        </div>
        <!-- 编辑按钮组 -->
        <div class="book-base-info--edit-group">
          <!-- 编辑按钮 -->
          <div class="book-base-info--edit">
            <i class="fa fa-edit"></i>
          </div>
          <!-- 确认和取消按钮（默认隐藏） -->
          <div class="book-base-info--edit-actions">
            <div class="book-base-info--confirm">
              <i class="fa fa-check"></i>
            </div>
            <div class="book-base-info--cancel">
              <i class="fa fa-times"></i>
            </div>
          </div>
        </div>
        <!-- 删除按钮 -->
        <div class="book-base-info--delete">
          <i class="fa fa-trash-can"></i>
        </div>
      </div>
    </div>
    <hr class="base-hr" />
    <!-- 书本内容 -->
    <div class="book-content-block">
      <div id="viewer" class="book-content-img-box">
        <!-- 遍历书本所有图片 -->
        {% for image_item in li_image_page %}
          <div class="book-img-wrapper" data-img-box-id="{{ image_item.id }}">
            <img data-src="{{ image_item.img_path.url }}"
                 src="{% static 'img/blank.png' %}"
                 alt="page:{{ image_item.page }} order:{{ image_item.order }}"
                 class="single-book-img lozad"
                 width="auto"
                 height="auto"
                 data-page-id="{{ image_item.id }}" />
            <i class="fa fa-trash book-img-delete-btn"
               data-page-id-del-btn="{{ image_item.id }}"></i>
          </div>
        {% endfor %}
      </div>
    </div>
    {% if pre_link or next_link %}
      <hr class="base-hr" />
      <div class="book-navigation">
        <div>
          {% if pre_link %}<a href="{{ pre_link }}">上一册</a>{% endif %}
        </div>
        <div>
          {% if next_link %}<a href="{{ next_link }}">下一册</a>{% endif %}
        </div>
      </div>
    {% endif %}
    <hr class="base-hr" />
    <!-- 其它的专题图册 -->
    <div class="related-subjects-section">
      {% for subject in li_subject_book %}
        <div class="related-subject-header">
          <div class="related-subject-title">
            <i class="fa-solid fa-folder-open subject-icon"></i>
            <span>专题：</span>
          </div>
          <a href="{% url 'subject_v3_detail' subject.id %}"
             class="related-subject-link">{{ subject.title }}</a>
        </div>
        <div class="same-subject-box">
          {% for same_subject_book in subject.li_book %}
            <a href="{% url 'book_v3' same_subject_book.id %}"
               class="same-subject-box__book-link">
              <div class="same-subject-box__book {% if same_subject_book.id == book_id %}same-subject-box__book__active{% endif %}">
                <img class="same-subject-book-thumb lozad"
                     data-src="{{ same_subject_book.thumb_url }}"
                     src="{% static 'img/blank.png' %}"
                     alt="{{ same_subject_book.title }}" />
                <div class="same-subject-book-title">{{ same_subject_book.asc_priority }} {{ same_subject_book.title }}</div>
              </div>
            </a>
          {% endfor %}
        </div>
        <hr class="subject-divider">
      {% endfor %}
    </div>
  </div>
{% endblock content %}
<!-- 自定义js -->
{% block js %}
  <!-- lozad 图片懒加载 js -->
  <script type="text/javascript" src="{% static 'js/lozad.min.js' %}"></script>
  <!-- viewer 图片预览 js -->
  <script type="text/javascript" src="{% static 'js/viewer.min.js' %}"></script>
  <!-- 拖动排序 -->
  <script type="text/javascript" src="{% static 'js/sortable.min.js' %}"></script>
  <!-- 书本详情 js -->
  <script type="text/javascript" src="{% static 'js/v3/book_v3.js' %}"></script>
{% endblock js %}
