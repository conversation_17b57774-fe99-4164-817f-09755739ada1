{% extends "media_station/v3/_base.html" %}
{% load static %}
{% block head %}
  <!-- 自定义css -->
  <link rel="stylesheet" href="{% static 'css/v3/index_v3.css' %}" />
  <link rel="stylesheet" href="{% static 'css/v3/index_v3__media.css' %}" />
{% endblock head %}
{% block content %}
  <!-- 修改操作栏结构 -->
  <div class="operation-bar">
    <!-- 折叠按钮 -->
    <button class="operation-toggle">
      <i class="fa-solid fa-angles-up"></i>
    </button>
    <div class="operation-container">
      <!-- 搜索框 -->
      <div class="operation-item search-box">
        <i class="fas fa-search"></i>
        <input type="text" id="searchInput" placeholder="搜索..." />
      </div>
      <!-- 类型筛选 -->
      <div class="operation-item filter-box filter-genre-block">
        <i class="fa fa-filter"></i>
        <select id="genreFilter">
          <option value="">全部类型</option>
          {% for genre_item in li_genres %}
            <option value="{{ genre_item.value }}"
                    {% if genre_item.count == 0 %}disabled{% endif %}>
              {{ genre_item.value }} ({{ genre_item.count }})
            </option>
          {% endfor %}
        </select>
      </div>
      <!-- 标签筛选 -->
      <div class="operation-item filter-box tag-filter">
        <div class="tag-filter-header" id="tagFilterHeader">
          <i class="fas fa-tags"></i>
          <span class="placeholder">全部标签</span>
        </div>
        <div class="tag-filter-dropdown" id="tagFilterDropdown">

          <!-- 添加标签 -->
          <div class="add-tag">
            <i class="fas fa-plus-circle" id="addTagBtn"></i>
          </div>

          <div class="tag-filter-content">
            {% for tag_item in li_tags %}
              <div class="tag-option {% if tag_item.count == 0 %}disabled{% endif %}"
                   data-value="{{ tag_item.value }}"
                   data-count="{{ tag_item.count }}">{{ tag_item.value }} ({{ tag_item.count }})</div>
            {% endfor %}
          </div>
        </div>
      </div>
      <!-- 无类型 -->
      <div class="operation-item opertaion-item-check-box">
        <label>
          <input type="checkbox" id="noGenreCheckbox" />
          无类型
        </label>
      </div>
      <!-- 无标签 -->
      <div class="operation-item opertaion-item-check-box">
        <label>
          <input type="checkbox" id="noTagsCheckbox" />
          无标签
        </label>
      </div>
      <!-- 无专题 -->
      <div class="operation-item opertaion-item-check-box">
        <label>
          <input type="checkbox" id="noSubjectCheckbox" />
          无专题
        </label>
      </div>
      <!-- 我的收藏 -->
      <div class="operation-item opertaion-item-check-box">
        <label>
          <input type="checkbox" id="myFavoriteCheckbox" />
          <i class="fas fa-star"></i>
          我的收藏
        </label>
      </div>
      <!-- 无图 -->
      <div class="operation-item opertaion-item-check-box">
        <label>
          <input type="checkbox" id="noImageCheckbox" />
          无图
        </label>
      </div>
      <!-- 随机按钮 -->
      <div class="operation-item">
        <button id="randomBtn" class="btn-normal">
          <i class="fas fa-random"></i>
          随机
        </button>
      </div>
      <!-- 重置按钮 -->
      <div class="operation-item">
        <button id="resetFilters" class="btn-danger btn-reset">
          <i class="fas fa-undo"></i>
          重置
        </button>
      </div>
      <!-- 刷新 -->
      <div class="operation-item">
        <button id="refreshBtn" class="btn-primary">
          <i class="fas fa-refresh"></i>
          刷新
        </button>
      </div>
      <!-- 合并按钮 -->
      <div class="operation-item">
        <button id="mergeBtn" class="btn-success">
          <i class="fas fa-object-group"></i>
          合并
        </button>
        <div id="mergeBtnGroup" style="display:none;">
          <button id="mergeCancelBtn" class="btn-secondary btn-sm">取消</button>
          <button id="mergeSubmitBtn" class="btn-success btn-sm">操作</button>
        </div>
      </div>
    </div>
    <!-- 分页与数量 -->
    <div class="pagination-box">
      <!-- 数量显示 -->
      <div class="book-count">
        共 <span class="count-number">0</span> 本
        <span class="page-size-control">
          每页
          <select id="pageSizeSelect">
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          条
        </span>
      </div>
      <!-- 分页组件 -->
      <div class="pagination">
        <button class="prev-page" disabled>&lt;</button>
        <div class="page-numbers">
          <!-- 这里将由 JavaScript 动态填充页码 -->
        </div>
        <button class="next-page" disabled>&gt;</button>
      </div>
    </div>
  </div>
  <div class="container">
    <!-- 添加图书网格容器 -->
    <div class="book-grid">
      <!-- 由js动态填充 -->
    </div>
  </div>
{% endblock content %}
<!-- 自定义js -->
{% block js %}
  <script type="text/javascript" src="{% static 'js/sortable.min.js' %}"></script>
  <script type="text/javascript" src="{% static 'js/v3/index_v3.js' %}"></script>
{% endblock js %}
