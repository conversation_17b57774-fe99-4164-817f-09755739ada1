{% extends "media_station/v3/_base.html" %}
{% load static %}
{% block head %}
    <link rel="stylesheet" href="{% static 'css/v3/subject_v3.css' %}" />
    <link rel="stylesheet" href="{% static 'css/v3/subject_v3__media.css' %}" />
    <meta name="description" content="专题列表页" />
{% endblock head %}
{% block content %}
    <div class="action-bar">
        <!-- 添加专题 -->
        <button class="add-subject-btn" title="添加专题">
            <i class="fa-solid fa-plus"></i>
        </button>
        <!-- 排序 -->
        <button class="sort-btn" title="排序">
            <i class="fa-solid fa-arrow-up-wide-short"></i>
        </button>
        <div class="sort-actions">
            <!-- 排序-确认 -->
            <button class="sort-confirm" title="确认">
                <i class="fa fa-check"></i>
            </button>
            <!-- 排序-取消 -->
            <button class="sort-cancel" title="取消">
                <i class="fa fa-times"></i>
            </button>
        </div>
    </div>
    <div class="subject-list">
        {% if li_subjects %}
            {% for subject in li_subjects %}
                <div class="subject-item" data-id="{{ subject.id }}">
                    <a href="{% url 'subject_v3_detail' subject.id %}"
                       title="{{ subject.title }}">
                        <div class="subject-thumb">
                            <img src="{{ subject.get_thumb_url }}"
                                 alt="{{ subject.title }}"
                                 width="100%"
                                 height="100%">
                        </div>
                        <div class="subject-info">
                            <div class="subject-title">
                                <i class="fas fa-layer-group"></i>
                                {{ subject.title }}
                            </div>
                            <div class="my-small-gray-text">
                                <i class="fa-solid fa-book"></i>
                                {{ subject.get_book_count }}
                            </div>
                        </div>
                    </a>
                </div>
            {% endfor %}
        {% else %}
            <div class="no-data">暂无专题内容</div>
        {% endif %}
    </div>
{% endblock content %}
{% block js %}
    <script type="text/javascript" src="{% static 'js/sortable.min.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/v3/subject_v3.js' %}"></script>
{% endblock js %}
