{% extends "media_station/v3/_base.html" %}
{% load static %}
{% block head %}
    <link rel="stylesheet" href="{% static 'css/v3/subject_details_v3.css' %}" />
    <link rel="stylesheet" href="{% static 'css/v3/subject_details_v3__media.css' %}" />
{% endblock head %}
{% block content %}
    <div class="subject-detail" data-id="{{ subject.id }}">
        <!-- 专题信息区域 -->
        <div class="subject-header-wrapper">
            <div class="subject-info">
                <div class="subject-header">
                    <div class="subject-title">{{ subject.title }}</div>

                    <!-- 书本数量 -->
                    <div class="subject-book-count">
                        <i class="fa-solid fa-book"></i>
                        <span>{{ subject.get_book_count }}</span>
                    </div>
                </div>
                <div class="subject-cover">
                    <img src="{{ subject.get_thumb_url }}"
                         alt="{{ subject.title }}"
                         width="200"
                         height="200">
                    <div class="subject-create-time">创建于 {{ subject.created_at|date:"Y-m-d" }}</div>
                </div>
            </div>
            <!-- 操作栏 -->
            <div class="action-bar">
                <button class="sort-btn" title="排序">
                    <i class="fa-solid fa-arrow-up-wide-short"></i>
                </button>
                <div class="sort-actions">
                    <button class="sort-confirm" title="确认">
                        <i class="fa fa-check"></i>
                    </button>
                    <button class="sort-cancel" title="取消">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
        </div>

        <hr class="base-hr">

        <!-- 图书列表 - 由JS加载 -->
        <div class="book-list">
            <div class="loading">加载中...</div>
        </div>
    </div>
{% endblock content %}
{% block js %}
    <script type="text/javascript" src="{% static 'js/sortable.min.js' %}"></script>
    <script type="text/javascript"
            src="{% static 'js/v3/subject_details_v3.js' %}"></script>
{% endblock js %}
