{% load static %}
<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <title>Books</title>
    <!-- ico -->
    <link rel="shortcut icon"
          href="{% static 'img/book.ico' %}"
          type="image/x-icon" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <!-- 公共css -->
    <link rel="stylesheet" href="{% static 'css/v3/base.css' %}" />
    <link rel="stylesheet" href="{% static 'css/v3/base__media.css' %}" />
    <!-- 添加 Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/font-awesome.6.5.1.min.css' %}" />
    <link rel="stylesheet" href="{% static 'webfonts/fa-solid-900.woff2' %}" />
    <!-- 自定义head -->
    {% block head %}
    {% endblock head %}
  </head>
  <body>
    <div id="topAnchor"></div>
    <!-- 添加导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <!-- 左侧 -->
        <div class="nav-left">
          <div class="nav-logo">
            <a href="{% url 'index_v3' %}">
              <i class="fas fa-book"></i>
              <span>Books</span>
            </a>
          </div>
          <div class="nav-search">
            <input type="text" id="navSearchInput" placeholder="搜索..." />
          </div>
        </div>

        <!-- 右侧 -->
        <ul class="nav-links">
          <li>
            <a href="{% url 'index_v3' %}" {% if request.path == '/v3/' %}class="active"{% endif %}><i class="fas fa-home"></i> 首页</a>
          </li>
          <li>
            <a href="{% url 'subjects_v3' %}" {% if request.path == '/v3/subjects/' %}class="active"{% endif %}><i class="fas fa-layer-group"></i> 专题</a>
          </li>
          <li>
            <a href="{% url 'upload' %}"><i class="fas fa-upload"></i> 上传</a>
          </li>
          <li>
            <a href="{% url 'index' %}"><i class="fas fa-cog"></i> 旧版</a>
          </li>
        </ul>
      </div>
    </nav>
    <!-- 主要内容 -->
    {% block content %}
    {% endblock content %}
    <!-- 滚到顶部 -->
    <button class="go-to-up-btn" onclick="window.scrollTo(0, 0)">
      <i class="fa fa-arrow-up"></i>
    </button>
    <!-- 滚到顶部 -->
    <button class="go-to-down-btn"
            onclick="window.scrollTo(0, document.body.scrollHeight)">
      <i class="fa fa-arrow-down"></i>
    </button>
    <!-- 公共js -->
    <script src="{% static 'js/v3/base_v3.js' %}"></script>
    <script>
      // 顶部导航搜索框回车跳转
      document.addEventListener('DOMContentLoaded', function() {
        var navInput = document.getElementById('navSearchInput');
        if(navInput) {
          navInput.addEventListener('keydown', function(e) {
            if(e.key === 'Enter') {
              var val = navInput.value.trim();
              if(val) {
                window.location.href = '/v3/?key=' + encodeURIComponent(val);
              }
            }
          });
        }
      });
    </script>
    <!-- 自定义js -->
    {% block js %}
    {% endblock js %}
  </body>
</html>
