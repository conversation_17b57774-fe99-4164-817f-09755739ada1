{% extends '_link.html' %}

{% load static %}

{% block content %}
    <style>
        {#最外层#}
        .main-box {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
        }


        .img-block-for-table {
            max-width: 80px;
            max-height: 60px;
            /* 包含，全包 */
            object-fit: cover;
        }

        .title-block-for-table {
            font-size: 13px;
            font-weight: 600;
        }

        .tags-block-for-table {
            width: 100%;
            font-size: 14px;
            position: relative;
        }

        .tags-block-for-table i {
            position: absolute;
            right: 0;
            top: 4px;
            visibility: hidden;
            cursor: pointer;
            color: #007bff;
        }

        .tags-block-for-table:hover i {
            visibility: unset;
        }


        .collect-block {
            position: absolute;
            right: 2px;
            top: 0;
        }

        .views-block {
            position: absolute;
            right: 2px;
            top: 24px;
            font-size: 12px;
            opacity: 0.7;
        }

        .collect-item {
            color: #f15806;
            cursor: pointer;
            font-size: 16px;
        }

        .collect-item:hover {
            color: #f57e3a;
        }

        .el-checkbox {
            margin-right: 0;
        }

        .no-img-pad {
            width: 160px;
            height: 160px;
        }


        .table_subject_block {
            position: relative;
        }

        .table_subject_block i {
            position: absolute;
            right: 0;
            top: 4px;
            visibility: hidden;
            cursor: pointer;
            color: #007bff;
        }

        .table_subject_block:hover i {
            visibility: unset;
        }


        /*小于 移动设备标准*/
        @media screen and (max-width: 768px) {
            .main-box {
                justify-content: space-around;
            }

            .no-img-pad {
                width: 100px;
                height: 100px;
            }


            .el-dialog {
                width: 80%;
            }

            .img-block-for-table {
                max-width: 64px;
                max-height: 48px;
            }

            .title-block-for-table, .tags-block-for-table {
                font-size: 12px;
            }
        }
    </style>

    <div id="app">
        <div>
            <el-input v-model="form.key" size="mini" class="opr-search" placeholder="搜索 key" clearable></el-input>

            <el-select v-model="form.genre" size="mini" placeholder="选择 genres" clearable>
                {% for genre_item in li_genres %}
                    <el-option label="{{ genre_item.value }}"
                               value="{{ genre_item.value }}">
                        <div style="display: flex;justify-content: space-between">
                            <span>{{ genre_item.value }}</span>
                            <div>
                                <span class="badge badge-pill badge-secondary">{{ genre_item.get_book_count }}</span>
                            </div>
                        </div>
                    </el-option>
                {% endfor %}
            </el-select>

            <el-button size="mini" @click="readyToSelectTags">
                选择 Tags
                <template v-if="form.li_tags.length">
                    <span>:</span>
                    <span v-for="tVal,tIndex in form.li_tags">
                        {[ tVal ]}
                        <span v-if="tIndex!=form.li_tags.length-1">,</span>
                    </span>
                </template>
            </el-button>

            <el-checkbox v-model="form.no_genre" border size="mini" :true-label="1" :false-label="0">无类型</el-checkbox>

            <el-checkbox v-model="form.no_tags" border size="mini" :true-label="1" :false-label="0">无标签</el-checkbox>

            <el-checkbox v-model="form.no_subject" border size="mini" :true-label="1" :false-label="0">无专题</el-checkbox>

            <el-checkbox v-model="form.is_collect" border size="mini" :true-label="1" :false-label="0">
                <i class="el-icon-star-on"></i>
                收藏
            </el-checkbox>

            <el-button size="mini" @click="reset" type="danger" plain>重置</el-button>
            <el-button size="mini" @click="loadData" type="primary" plain>刷新</el-button>

            <el-button type="warning" size="mini" @click="readyToUpdateGenre(books.list.map(item => item.id))" plain>
                批量设置类型
            </el-button>

            <el-button type="success" size="mini" @click="readyToBindTags(books.list.map(item => item.id), 0)" plain>
                批量追加标签
            </el-button>

            <el-radio-group v-model="showFormat" size="mini" class="my_show_format"
                            @input="changeShowFormat">
                <el-radio-button label="grid">
                    <i class="el-icon-s-grid"></i>
                </el-radio-button>
                <el-radio-button label="table">
                    <i class="el-icon-s-unfold"></i>
                </el-radio-button>
            </el-radio-group>


            <el-checkbox v-model="showImg" border size="mini">
                图片
            </el-checkbox>

            <el-checkbox v-model="form.order_by_views_asc" true-label="1" false-label="0" border size="mini">
                +阅读
            </el-checkbox>

            <el-checkbox v-model="form.order_by_created_at_asc" true-label="1" false-label="0" border size="mini">
                +时间
            </el-checkbox>

            <el-checkbox v-model="form.is_random" true-label="1" false-label="0" border size="mini">
                随机
            </el-checkbox>

            <!-- 标签-选择-弹窗 -->
            <el-dialog title="筛选 标签"
                       :visible.sync="selectTag.visible"
                       width="80%">

                <el-checkbox-group v-model="selectTag.liTmp" size="mini">
                    {% for tag_item in li_tags %}
                        <el-checkbox label="{{ tag_item.value }}" border>
                            <div style="display: flex;justify-content: space-between">
                                <div>{{ tag_item.value }}</div>
                                <div style="margin-left: 4px">
                                    <span class="badge badge-pill badge-secondary">{{ tag_item.get_book_count }}</span>
                                </div>
                            </div>
                        </el-checkbox>
                    {% endfor %}
                </el-checkbox-group>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="selectTag.visible = false" size="mini">取 消</el-button>
                    <el-button type="primary" @click="goSelectTags" size="mini">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 标签-维护-弹窗 -->
            <el-dialog
                    :title="`${isUpdate?'修改':'追加'} 标签`"
                    :visible.sync="bindTag.visible">
                <div>
                    <el-checkbox-group v-model="bindTag.liTags" size="mini">
                        {% for tag_item in li_tags %}
                            <el-checkbox size="mini" label="{{ tag_item.value }}" border></el-checkbox>
                        {% endfor %}
                    </el-checkbox-group>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="bindTag.visible = false" size="mini">取 消</el-button>
                    <el-button type="primary" @click="goBindTag" size="mini">确 定</el-button>
                </div>
            </el-dialog>

            <el-dialog
                    title="批量设置类型"
                    :visible.sync="setGenre.visible">
                <div>
                    <el-select v-model="setGenre.value" size="mini" placeholder="选择 genres" clearable
                               style="width: 100%">
                        {% for genre_item in li_genres %}
                            <el-option label="{{ genre_item.value }}"
                                       value="{{ genre_item.value }}">
                                <div style="display: flex;justify-content: space-between">
                                    <span>{{ genre_item.value }}</span>
                                    <div>
                                        <span class="badge badge-pill badge-secondary">{{ genre_item.get_book_count }}</span>
                                    </div>
                                </div>
                            </el-option>
                        {% endfor %}
                    </el-select>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="setGenre.visible = false" size="mini">取 消</el-button>
                    <el-button type="primary" @click="goSetGenre" size="mini">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 专题-绑定-弹窗 -->
            <el-dialog title="绑定 专题"
                       :visible.sync="bindSubject.visible">
                <div style="display: flex;align-items: center;flex-wrap: wrap;">
                    {% for subject in li_subjects %}
                        <div class="subject-link-book"
                             @click="goBindSubject({{ subject.id }})">
                            {# 图片#}
                            <img src="{{ subject.get_thumb_url }}" alt="">
                            <div>
                                {{ subject.title }}
                            </div>
                        </div>

                    {% endfor %}
                </div>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="bindSubject.visible = false" size="mini">取 消</el-button>
                </div>
            </el-dialog>
        </div>
        <div style="margin-top: 8px">
            <el-pagination v-if="isMobile"
                           small
                           layout="prev, pager, next"
                           :total="books.count"
                           :current-page="form.page"
                           :page-sizes="[10, 20, 50, 100]"
                           :page-size="form.size"
                           @current-change="changePage"
                           @size-change="changeSize">
            </el-pagination>

            <el-pagination v-else
                           small
                           background
                           layout="total, prev, pager, next, sizes"
                           :total="books.count"
                           :current-page="form.page"
                           :page-sizes="[10, 20, 50, 100]"
                           :page-size="form.size"
                           @current-change="changePage"
                           @size-change="changeSize">
            </el-pagination>
        </div>

        {# 遍历图书#}
        {# ———— 格子显示 ———— #}
        <div v-if="showFormat==='grid'">
            <div class="main-box" v-loading="loading">
                <div class="book-block" v-for="(book_item,book_index) in books.list" :title="book_item.title">
                    <a :href=`/book/${book_item.id}` target="_blank" style="color: #0b2e13;overflow: hidden">
                        {# 图片#}
                        <img v-show="showImg"
                             class="book-img-block"
                             :src="`${book_item.first_img}`"
                             alt="">
                        <div v-show="!showImg" class="no-img-pad">&nbsp;</div>
                    </a>

                    <div class="main-book-info">
                        {# 标题区#}
                        <a :href=`/book/${book_item.id}` target="_blank"
                           style="color: #0b2e13;overflow: hidden">
                            <div class="book-block--title">{[ book_item.title ]} ({[ book_item.page_count ]})</div>
                        </a>

                        <!-- 类型与标签 -->
                        <div class="genre-and-tags-block">
                            <div class="book-genre-block">
                                <el-dropdown size="mini" split-button type="primary"
                                             @click="goLink(`/?genre=${book_item.genre}`)"
                                             @command="choseGenre(book_item.id)"
                                             class="my_book_genre_btn">
                                    {[ book_item.genre || '—' ]}
                                    <el-dropdown-menu slot="dropdown">
                                        {% for genre_item in li_genres %}
                                            <el-dropdown-item command="{{ genre_item.value }}">
                                                {{ genre_item.value }}
                                            </el-dropdown-item>
                                        {% endfor %}
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </div>

                            <div class="book-tags-block">
                                <a v-for="tag in book_item.tags" :href=`/?tag=${tag.value}` target="_blank">
                                    <span class="badge badge-pill badge-success">{[ tag.value ]}</span>
                                </a>
                                <span>&nbsp;</span>

                                <i class="el-icon-edit" @click="readyToBindTags([ book_item.id ], 1)"></i>
                            </div>
                        </div>

                        {# 收藏#}
                        <div class="collect-block">
                            <i :class=`el-icon-star-${book_item.is_collect?'on':'off'}`
                               class="collect-item"
                               @click="collect(`${book_item.id}`,book_index)"></i>
                        </div>
                        {# 浏览量#}
                        <div class="views-block">
                            <i class="el-icon-thumb"></i>
                            {[ book_item.views ]}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {#      表格显示#}
        <div v-else>
            <div class="main-box" v-loading="loading">
                <el-table :data="books.list"
                          stripe
                          border
                          style="width: 100%;margin-top: 4px"
                          size="mini">

                    {# 图片 #}
                    <el-table-column prop="first_img" label="封面" v-if="showImg">
                        <template slot-scope="scope">
                            <a :href=`/book/${scope.row.id}` target="_blank"
                               style="color: #0b2e13;">
                                <img class="img-block-for-table"
                                     :src="`${scope.row.first_img}`"
                                     alt="">
                            </a>
                        </template>
                    </el-table-column>

                    {# 标题 #}
                    <el-table-column prop="title"
                                     label="标题（页数）"
                                     min-width="200px">
                        <template slot-scope="scope">
                            <a :href=`/book/${scope.row.id}` target="_blank"
                               style="color: #0b2e13;min-width: 150px;display: inline-block">
                                <div class="title-block-for-table">{[ scope.row.title ]} ({[ scope.row.page_count ]})
                                </div>
                            </a>
                        </template>
                    </el-table-column>

                    {# 类型#}
                    <el-table-column prop="tags" label="类型" v-if="!isMobile">
                        <template slot-scope="scope">
                            <div>
                                <el-dropdown size="mini" split-button type="primary"
                                             @click="goLink(`/?genre=${scope.row.genre}`)"
                                             @command="choseGenre(scope.row.id)"
                                             class="my_book_genre_btn">
                                    {[ scope.row.genre || '—' ]}
                                    <el-dropdown-menu slot="dropdown">
                                        {% for genre_item in li_genres %}
                                            <el-dropdown-item command="{{ genre_item.value }}">
                                                {{ genre_item.value }}
                                            </el-dropdown-item>
                                        {% endfor %}
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </div>

                        </template>
                    </el-table-column>

                    {# 标签 #}
                    <el-table-column
                            prop="tags"
                            label="标签"
                            v-if="!isMobile">
                        <template slot-scope="scope">
                            <div class="tags-block-for-table">
                                <a v-for="tag in scope.row.tags" :href=`/?tag=${tag.value}`>
                                    <span class="badge badge-pill badge-success">{[ tag.value ]}</span>
                                </a>
                                <span>&nbsp;</span>

                                <i class="el-icon-edit" @click="readyToBindTags([scope.row.id], 1, scope.row.tags)"></i>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="专题"
                            v-if="!isMobile">
                        <template slot-scope="scope">
                            <div class="table_subject_block">
                                <div>
                                    <div v-for="subject in scope.row.li_subjects"
                                         style="margin-bottom: 2px;">
                                        <a :href="`/subjects/${subject.id}`"
                                           style="display: inline-block;">
                                            <div style="display: flex;align-items: center;">
                                                <img :src="subject.thumb_url"
                                                     style="min-width: 20px;max-width: 20px" alt="">
                                                <div style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;margin-left: 8px">
                                                    {[ subject.title ]}
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <span>&nbsp;</span>
                                <i class="el-icon-circle-plus"
                                   @click="readyToBindSubject(scope.row.id)"></i>
                            </div>
                        </template>
                    </el-table-column>

                    {# 收藏#}
                    <el-table-column
                            prop="collect"
                            label="收藏"
                            width="50px">
                        <template slot-scope="scope">
                            <i :class=`el-icon-star-${scope.row.is_collect?'on':'off'}`
                               class="collect-item"
                               @click="collect(`${scope.row.id}`,scope.$index)"></i>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>


        <div style="margin: 8px 0">
            <el-pagination v-if="isMobile"
                           small
                           layout="prev, pager, next"
                           :total="books.count"
                           :current-page="form.page"
                           :page-sizes="[10, 20, 50, 100]"
                           :page-size="form.size"
                           @current-change="changePage"
                           @size-change="changeSize">
            </el-pagination>

            <el-pagination v-else
                           small
                           background
                           layout="total, prev, pager, next, sizes"
                           :total="books.count"
                           :current-page="form.page"
                           :page-sizes="[10, 20, 50, 100]"
                           :page-size="form.size"
                           @current-change="changePage"
                           @size-change="changeSize">
            </el-pagination>
        </div>
    </div>

    <script>
        var vm = new Vue({
            delimiters: ["{[", "]}"],   // 将 vue 的分隔符重定义为 {[
            el: "#app",
            data: {
                loading: false,
                books: {
                    list: [],
                    count: 0
                },

                selectTag: {
                    visible: false,
                    liTmp: []
                },

                form: {
                    key: '',
                    page: 1,
                    size: 50,
                    genre: '',
                    li_tags: [],
                    is_collect: 0,  // 只看 收藏
                    no_genre: 0, // 只看 无类型
                    no_tags: 0,  // 只看 无标签
                    no_subject: 0,   // 只看 无专题
                    order_by_views_asc: 0,  // 浏览量正序 排列
                    order_by_created_at_asc: 0,  // 时间正序 排列
                    only_subject: 0,    // 默认 忽略 仅专题
                    is_random: 0,    // 随机
                },
                mobileMaxWidth: 768,

                showFormat: 'grid', // 0-grid 格子， 1-table 表格

                showImg: true,  // 显示图片

                bindBookIdTmp: [],   // 临时数组

                // 批量绑定标签
                isUpdate: 0, // 0-追加，1-修改
                bindTag: {
                    visible: false,
                    liTags: []
                },

                // 修改类型
                setGenre: {
                    visible: false,
                    value: ''
                },

                // 绑定 专题
                bindSubject: {
                    visible: false,
                    bookId: null,    // 当前 book id
                    subjectId: null,    // 准备绑定的 专题id
                }
            },
            methods: {
                reset() {
                    this.form = {
                        key: '',
                        page: 1,
                        size: 50,
                        genre: '',
                        li_tags: [],
                        is_collect: 0,  // 只看 收藏
                        no_genre: 0, // 只看 无类型
                        no_tags: 0,  // 只看 无标签
                        no_subject: 0,   // 只看 无专题
                        order_by_views_asc: 0,  // 浏览量正序 排列
                        order_by_created_at_asc: 0,  // 时间正序 排列
                        only_subject: 0,    // 默认 忽略 仅专题
                        is_random: 0 // 随机
                    }
                    this.selectTag.liTmp = []
                },
                changeShowFormat(val) {
                    console.log(`/?showFormat=${val}`)
                },
                changePage(page) {
                    this.form.page = page
                },
                changeSize(size) {
                    this.form.size = size
                },
                collect(bookId, bookIndex) {
                    $.ajax({
                        type: 'POST',
                        url: '/aj_collect/',
                        data: {id: bookId},
                        success: (data) => {
                            if (data.code === 0) {
                                let dom = $(`.collect-item:eq(${bookIndex})`)

                                // 修改 收藏效果
                                if (data.data === 1) {  // 已收藏
                                    dom.removeClass('el-icon-star-off')
                                    dom.addClass('el-icon-star-on')
                                } else {  // 未收藏
                                    dom.removeClass('el-icon-star-on')
                                    dom.addClass('el-icon-star-off')
                                }

                                this.$message.success('收藏成功');
                            } else {
                                alert('Error: ' + data.msg);
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                loadData() {
                    this.loading = true
                    $.ajax({
                        type: 'GET',
                        url: '/aj_book/list/',
                        data: this.form,
                        success: (data) => {
                            if (data.code === 0) {
                                this.books.list = data.data.list;
                                this.books.count = data.data.count;
                            } else {
                                alert('Error: ' + data.error_message);
                            }
                            this.loading = false
                        },
                        error: function () {
                            alert('Error');
                            this.loading = false
                        }
                    })
                },
                goLink(link) {
                    {#location.href = link#}
                    window.open(link, '_blank')
                },
                /**
                 * 下拉 事件
                 */
                choseGenre(bookId) {
                    console.log(bookId)
                    console.log(event.target.innerText)

                    this.setGenre.value = event.target.innerText
                    this.bindBookIdTmp = [bookId]

                    // 直接提交 修改
                    this.goSetGenre()
                },
                /**
                 * 准备-修改 类型
                 * @param liBookId
                 */
                readyToUpdateGenre(liBookId) {
                    this.bindBookIdTmp = liBookId
                    this.setGenre.visible = true
                },
                readyToBindTags(liBookId, isUpdate = 0, liTags) {
                    this.isUpdate = isUpdate
                    this.bindBookIdTmp = liBookId
                    if (liTags) {
                        this.bindTag.liTags = liTags.map(item => item.value)    // 渲染 已选中数据
                    }
                    this.bindTag.visible = true
                },
                goBindTag() {
                    let urlMapper = {
                        0: '/aj_batch_bind_tags/',    // 追加
                        1: '/aj_batch_update_tags/'   // 修改
                    }
                    let url = urlMapper[this.isUpdate]
                    $.ajax({
                        type: 'POST',
                        url: url,
                        data: {
                            li_books_id: this.bindBookIdTmp,
                            li_tags: this.bindTag.liTags
                        },
                        success: (data) => {
                            if (data.code === 0) {
                                this.loadData()
                                this.$message.success(`${this.isUpdate ? '修改' : '追加'}成功`);
                            } else {
                                alert('Error: ' + data.msg);
                            }

                            // 关闭 模态框，重置选中
                            this.bindTag = {
                                visible: false,
                                liTags: []
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                goSetGenre() {
                    $.ajax({
                        type: 'POST',
                        url: '/aj_batch_set_genre/',
                        data: {
                            li_books_id: this.bindBookIdTmp,
                            genre: this.setGenre.value
                        },
                        success: (data) => {
                            if (data.code === 0) {
                                this.loadData()
                                this.$message.success('修改成功');
                            } else {
                                alert('Error: ' + data.msg);
                            }

                            // 关闭 模态框，重置选中
                            this.setGenre = {
                                visible: false,
                                value: ''
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                readyToBindSubject(bookId) {
                    this.bindSubject.visible = true;
                    this.bindSubject.bookId = bookId;
                },
                goBindSubject(subjectId) {
                    $.ajax({
                        type: 'POST',
                        url: `/aj_subject/${subjectId}/book/batch_add/`,
                        data: {
                            li_book_id: [this.bindSubject.bookId]
                        },
                        success: (data) => {
                            if (data.code === 0) {
                                this.loadData()
                                this.$message.success('绑定成功');
                            } else {
                                alert('Error: ' + data.msg);
                            }

                            // 关闭 模态框，重置选中
                            this.bindSubject = {
                                visible: false,
                                bookId: null,
                                subjectId: null
                            }
                        },
                        error: function () {
                            alert('Error');
                        }
                    })
                },
                getQuery() {
                    const tmp = location.href.split("?")[1];

                    if (!tmp) return

                    tmp.split('&').forEach(item => {
                        let [k, v] = item.split('=')
                        // decode uri 去掉地址栏转义
                        v = decodeURI(v);

                        if (k === 'tag') {
                            this.form.li_tags = [v]
                        }

                        if (k === 'genre') {
                            this.form.genre = v
                        }

                        if (k === 'showFormat') {
                            this.showFormat = v
                        }
                    })
                },
                readyToSelectTags() {
                    this.selectTag.visible = true
                },
                goSelectTags() {
                    this.form.li_tags = this.selectTag.liTmp
                    this.selectTag.visible = false
                }
            },
            computed: {
                isMobile() {
                    return document.body.clientWidth < this.mobileMaxWidth;
                }
            },
            watch: {
                form: {
                    deep: true,
                    handler: function () {
                        this.loadData()
                    }
                }
            },
            mounted() {
                this.getQuery() // 解析 地址栏 query
                this.loadData()
            }
        })
    </script>

{% endblock content %}

