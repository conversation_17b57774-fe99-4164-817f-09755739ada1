<style>
    {#单个图册#}
    .book-block {
        margin: 8px;
        border: 1px solid #eeeeee;
        border-radius: 2px;

        display: flex;
        flex-direction: column;

        position: relative;
    }

    .book-block:hover {
        border: 1px solid #d0d0d0;
    }

    .book-block:hover .title-block {
        color: #ea2a9e;
    }

    {#图册-图片#}
    .img-block {
        width: 160px;
        height: 160px;
        /* 包含，全包 */
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .img-block:hover {
        transform: scale(2);
    }

    {#图册-标题#}
    .title-block {
        /*文本不换行*/
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px;
        max-width: 150px;
        font-weight: 600;
    }

    .genre-block {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0.7;
    }


    .tags-block {
        overflow-x: auto;
        white-space: nowrap;
        display: flex;
        justify-content: flex-start;
        max-width: 160px;
        font-size: 12px;
        position: relative;
    }

    .tags-block i {
        position: absolute;
        right: 4px;
        top: 4px;
        visibility: hidden;
        cursor: pointer;
        color: #007bff;
    }

    .tags-block:hover i {
        visibility: unset;
    }

    .views-block {
        position: absolute;
        right: 2px;
        top: 24px;
        font-size: 12px;
        opacity: 0.7;
    }
</style>


<div class="book-block">
    <a href="/book/{{ book_item.id }}" target="_blank"
       style="color: #0b2e13;overflow: hidden">
        {# 图片#}
        <img class="img-block" src="{{ book_item.get_book_first_img }}" alt="">
    </a>

    {# 标题区#}
    <a href="/book/{{ book_item.id }}" target="_blank"
       style="color: #0b2e13;overflow: hidden">
        <div class="title-block">{{ book_item.title }} ({{ book_item.page_count }})</div>
    </a>

    <div class="genre-block">
        {{ book_item.genre }}
    </div>

    <div class="tags-block">
        {% for tag_item in book_item.get_tags %}
            <a href="/?tag={{ tag_item.value }}" target="_blank">
                <span class="badge badge-pill badge-success">{{ tag_item.value }}</span>
            </a>
        {% endfor %}
    </div>

    {# 浏览量#}
    <div class="views-block">
        <i class="el-icon-thumb"></i>
        {{ book_item.views }}
    </div>
</div>