{% load static %}

<!DOCTYPE html>
<html lang="zh" style="height: 100vh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
    <title>首页</title>
    <!-- ico -->
    <link rel="shortcut icon" href="{% static 'img/book.ico' %}" type="image/x-icon">

    <!-- bootstrap css -->
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- element 字体 -->
    <link href="{% static 'css/elementui/fonts/element-icons.ttf' %}" rel="stylesheet">
    <link href="{% static 'css/elementui/fonts/element-icons.woff' %}" rel="stylesheet">

    <!-- element css -->
    <link rel="stylesheet" href="{% static 'css/elementui/elementui_2.15.13.css' %}">

    <!-- 项目 css -->
    <link rel="stylesheet" href="{% static 'css/index.css' %}">
    <link rel="stylesheet" href="{% static 'css/project_base.css' %}">
    <link rel="stylesheet" href="{% static 'css/project_base_media.css' %}">

    <!-- jquery js -->
    <script type="text/javascript" src="{% static 'js/jquery-3.6.3.min.js' %}"></script>

    <!-- bootstrap js -->
    <script type="text/javascript" src="{% static 'js/bootstrap.min.js' %}"></script>

    <!-- vue js -->
    <script type="text/javascript" src="{% static 'js/vue/vue_2.6.12.js' %}"></script>

    <!-- element js -->
    <script type="text/javascript" src="{% static 'js/elementui/elementui_2.15.13.js' %}"></script>

    {% block sc %}
    {% endblock %}
</head>

<body>

<style>
    .base-content-block {
        padding: 70px 20px 8px;
    }

    /*小于 移动设备标准*/
    @media screen and (max-width: 768px) {
        .base-content-block {
            padding: 64px 4px 8px;
        }
    }
</style>


<div id="topAnchor"></div>

{#块导航栏#}
{% block navbar %}

    <nav class="navbar navbar-expand-lg navbar-dark fixed-top bg-dark">
        <a class="navbar-brand" href="/">Books</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item {% if request.path == '/' %} active {% endif %} ">
                    <a class="nav-link" href="/">Home <span class="sr-only">(current)</span></a>
                </li>

                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
                       aria-expanded="false">
                        Genres
                    </a>
                    <div class="dropdown-menu">
                        {% for genre_item in li_genres %}
                            <a class="dropdown-item"
                               href="/?genre={{ genre_item.value }}">
                                <div style="display: flex;justify-content: space-between">
                                    <span>{{ genre_item.value }}</span>
                                    <div>
                                        <span class="badge badge-pill badge-secondary">{{ genre_item.get_book_count }}</span>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </li>

                <li class="nav-item {% if request.path == '/subjects/' %} active {% endif %} ">
                    <a class="nav-link" href="/subjects/">Subjects</a>
                </li>

                <li class="nav-item {% if request.path == '/upload/' %} active {% endif %} ">
                    <a class="nav-link" href="/upload/">Upload</a>
                </li>

                <li class="nav-item {% if request.path == '/v3/' %} active {% endif %} ">
                    <a class="nav-link" href="/v3/">V3</a>
                </li>
            </ul>

        </div>
    </nav>

{% endblock navbar %}


<div class="base-content-block">
    {% block content %}
        content
    {% endblock content %}
</div>

<div id="endAnchor"></div>


{% block anchorJump %}
    {#返回顶部#}
    <a href="#topAnchor" class="back_to_top">
        <i class="el-icon-caret-top"></i>
    </a>

    {#返回顶部#}
    <a href="#endAnchor" class="go_to_end">
        <i class="el-icon-caret-bottom"></i>
    </a>
{% endblock %}

{#消息#}
<div>
    {% if messages %}
        <div class="messages"
             style="z-index: 9999;position: fixed;top: 50px;right: 4px;width: 20%;min-width: 100px;opacity: 0.7">
            {% for message in messages %}
                {% if message.tags == 'error' %}
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <strong>{{ message }}</strong>
                    </div>
                {% else %}
                    <div class="alert alert-success alert-dismissible" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <strong>{{ message }}</strong>
                    </div>
                {% endif %}

            {% endfor %}
        </div>
    {% endif %}
</div>

{#块底部内容#}
{% block footer %}
{% endblock footer %}


{% block script_block %}
{% endblock %}

</body>
</html>