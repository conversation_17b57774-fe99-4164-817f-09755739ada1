import json
import math
import os
import re
import shutil

from django.http import FileResponse, JsonResponse
from main.settings import MEDIA_ROOT
from media_station.constants import RESOURCE_SUB_DIR
from media_station.models import (
    BookCollections, BookPages, Books, Genres, Subject, SubjectBookLink, Tags,
)
from utils import out


def api_genres_list(reqeust):
    """类型-列表"""
    li_out = [
        {
            'id': item.id,
            'value': item.value,
            'book_count': item.get_book_count(),
        }
        for item in Genres.objects.order_by("value").all()
    ]
    return JsonResponse({"code": 0, "data": li_out})


def api_tags_list(request):
    """标签-列表"""
    li_out = [
        {
            'id': item.id,
            'value': item.value,
            'book_count': item.get_book_count(),
        }
        for item in Tags.objects.order_by("value").all()
    ]
    return JsonResponse({"code": 0, "data": li_out})


def api_subjects_list(request):
    """专题-列表"""

    values = request.GET

    key = values.get("key", "")
    page = int(values.get("page", 1))
    size = int(values.get("size", 50))

    start = (page - 1) * size

    res = Subject.objects

    if key:
        res = res.filter(title__icontains=key)

    order_block = "asc_priority", "-created_at"

    li_out = [
        item.subject_info()
        for item in res.order_by(*order_block)[start: start + size]
    ]

    count = res.count()

    return JsonResponse(
        {
            "code": 0,
            "data": {
                "list": li_out,
                "count": count,
                "size": size,
            },
        }
    )


def api_tag_add(request):
    """标签-添加"""

    jo = json.loads(request.body)
    tag_value = jo.get("value", "")

    if not tag_value:
        return JsonResponse({'code': 1, 'msg': '参数为空'})

    # 判重复
    db_exist = Tags.objects.filter(value=tag_value).first()
    if db_exist:
        return JsonResponse({'code': 1, 'msg': '错误: 重复'})

    # 添加
    Tags.objects.create(value=tag_value)
    return JsonResponse({'code': 0, 'msg': '添加成功'})


def api_books_list(request):
    value = request.GET

    key = value.get("key", "")
    page = int(value.get("page", 1))
    size = int(value.get("size", 10))
    genre = value.get("genre", "")
    tags_str = value.get("tags", "")
    li_tag = [i for i in tags_str.split(";") if i.strip()]
    no_genre = int(value.get("no_genre", 0))  # 无类型
    no_tags = int(value.get("no_tags", 0))  # 无标签
    no_subject = int(value.get("no_subject", 0))  # 无专题
    is_collect = int(value.get("is_collect", 0))  # 我的收藏
    is_random = int(value.get("is_random", 0))  # 随机

    start = (page - 1) * size

    res = Books.objects.filter(
        # 筛选 可见
        is_visible=True,
    )

    order_block = ("-id",)

    # 关键词搜索
    if key:
        res = res.filter(title__icontains=key)

    # 筛选 类型
    if genre:
        db_genre = Genres.objects.filter(value=genre).first()
        if db_genre:
            res = res.filter(genre_id=db_genre.id)
            order_block = "-order", "title"

    # 筛选 标签
    if li_tag:
        for tag_value in li_tag:
            res = res.filter(tags__value=tag_value)

    # 筛选 无类型
    if no_genre:
        res = res.filter(genre_id__isnull=True)
    # 筛选 无标签
    if no_tags:
        res = res.filter(tags__isnull=True)
    # 筛选 无专题
    if no_subject:
        res = res.filter(subjectbooklink__isnull=True)
    # 筛选 我的收藏
    if is_collect:
        res = res.filter(bookcollections__isnull=False)

    if is_random:  # 随机
        li_out = [
            item.book_info()
            for item in Books.objects.filter(is_visible=True).order_by("?")[:size]
        ]
    else:  # 正常
        li_out = [
            item.book_info()
            for item in res.order_by(*order_block)[start: start + size]
        ]

    count = res.count()
    total_page = math.ceil(count / size)
    return JsonResponse(
        {
            "code": 0,
            "data": {
                "list": li_out,
                "count": count,
                "size": size,
                "total_page": total_page,
                'is_end': total_page == page
            },
        }
    )


def api_books_combine(request):
    """书本-合并"""

    jo = json.loads(request.body)
    li_book_id = jo.get("li_book_id", "")

    # 获取 所有图书
    li_db_book = Books.objects.filter(pk__in=li_book_id).all()

    # 顺序 字典
    sort_order_di = {int(value): index for index,
                     value in enumerate(li_book_id)}
    # 排序后的 db book
    li_db_book = sorted(li_db_book,
                        key=lambda x: sort_order_di[x.id])

    # 遍历所有图册，获取标题，统计总图片数量
    titles = []
    total_page = 0
    all_tags = set()    # 收集标签
    all_subjects = set()    # 收集专题
    default_genre = None    # 第一个非空类型
    is_collect = False    # 是否已收藏

    for book_item in li_db_book:
        titles.append(book_item.title)
        total_page += book_item.get_book_page_count()

        # 收集标签
        all_tags.update(book_item.tags.all())

        # 收集专题
        for subject_link in book_item.subjectbooklink_set.all():
            all_subjects.add(subject_link.subject)

        # 记录第一个非空的类型作为默认类型
        if not default_genre and book_item.genre:
            default_genre = book_item.genre

        # 记录是否已收藏（只要有任意一个book已收藏）
        if book_item.get_is_collect():
            is_collect = True

    # 新的文件名（也是文件夹名）
    new_title = ';'.join(titles) + '--combine'
    new_title = re.sub(r'[^a-zA-Z0-9\u4e00-\u9fa5\-\_]',
                       '_', new_title)    # 将标题中的非法字符(不是 字母/数字/中文)替换为下划线

    # 保存路径
    target_save_path = os.path.join(
        MEDIA_ROOT,
        RESOURCE_SUB_DIR,
        new_title
    )

    # 创建文件夹
    if not os.path.exists(target_save_path):
        os.makedirs(target_save_path)

    # 创建 db book
    db_new_book = Books(
        title=new_title,
        remarks=f'合并来源：{"; ".join(titles)}',
        is_visible=True,
        genre=default_genre,  # 设置类型(有可能为空)
    )
    db_new_book.save()

    # 添加所有标签
    if all_tags:
        db_new_book.tags.set(all_tags)

    # 添加所有专题关联
    for subject in all_subjects:
        SubjectBookLink.objects.create(
            subject=subject,
            book=db_new_book
        )

    # 设置是否已收藏
    if is_collect:
        BookCollections.objects.create(
            book=db_new_book
        )

    # 循环复制 每本书-每一页到 新文件夹
    index_img = 0   # 图片索引
    for book_item in li_db_book:  # 遍历每本书
        # 遍历 书本的每一页
        for page_item in book_item.bookpages_set.order_by('-order', 'page').all():
            img_name = f'{index_img+1}.jpg'   # 图片名称

            # 最终图片路径
            final_img_path = os.path.join(target_save_path, img_name)

            # 复制图片
            shutil.copy(page_item.img_path.path, final_img_path)

            # 保存每一页到db
            db_book_page = BookPages(
                image=db_new_book,
                img_path=f'{RESOURCE_SUB_DIR}/{new_title}/{img_name}',
                page=index_img + 1,  # 页码
            )
            db_book_page.save()

            index_img += 1  # 索引+1

    # 更新页数
    db_new_book.page_count = index_img
    db_new_book.save()

    return JsonResponse(out.send(msg='合并成功'))


def _handle_zip_file_upload():
    pass


def _handle_folder_upload():
    pass


def api_books_upload(request):
    """书本-上传"""

    # 判断上传的是单个zip文件，还是一个文件夹
    if request.FILES.get('zip_file'):  # 上传的是zip文件
        _handle_zip_file_upload()
        return JsonResponse(out.send(msg='上传压缩文件成功'))

    elif request.FILES.get('folder'):  # 上传的是文件夹
        _handle_folder_upload
        return JsonResponse(out.send(msg='上传文件夹成功'))

    return JsonResponse(out.ERROR_OF_ILLEGAL_PARAM)


def api_book_details(request, book_id):
    """书本-详情"""

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        return JsonResponse({'code': 1, 'msg': '书本不存在'})

    db_book.views += 1  # 浏览量+1
    db_book.save()

    return JsonResponse(
        {
            'code': 0,
            'data': {
                'book': db_book.book_details(),
            }
        }
    )


def api_book_pages_list(request, book_id):
    """书本-页面-列表"""

    params = request.GET
    page = int(params.get('page', 1))
    size = int(params.get('size', 10))

    start = (page - 1) * size

    res = BookPages.objects.filter(
        image_id=book_id
    )

    count = res.count()
    total_page = math.ceil(count / size)

    li_out = [
        item.book_page_base_info()
        for item in res.order_by('-order', 'page')[start: start + size]
    ]

    return JsonResponse({
        'code': 0,
        'data': {
            'list': li_out,
            'count': count,
            'size': size,
            'total_page': total_page,
            'is_end': total_page == page
        }
    })


def api_book_del(request, book_id):
    """
    书本-删除
    """

    db_book = Books.objects.filter(pk=book_id).first()
    dir_name = db_book.get_book_save_dir_name()  # 获取文件夹

    Books.objects.filter(pk=book_id).delete()  # 删除 db

    # 删除 资源文件
    if dir_name:
        Books.delete_dir_with_title(dir_name)

    return JsonResponse(
        out.send(msg='删除book成功')
    )


def api_book_page_del(request, book_id, page_id):
    """删除 book 的 page"""

    # 删除 本地图片
    db_page = BookPages.objects.filter(pk=page_id).first()
    if db_page:
        os.remove(db_page.img_path.path)

    # 删除 db
    BookPages.objects.filter(pk=page_id).delete()

    # ———— 刷新 book 缓存 ————
    Books.fresh_cache(book_id)

    return JsonResponse(out.send(msg='删除book page 成功'))


def api_book_pages_sort(request, book_id):
    """book 图片重新排序"""

    li_page_id = json.loads(request.body)["li_page_id"]

    len_page = len(li_page_id)

    # 按顺序更新 order，order 越大越靠前
    for index, page_id in enumerate(li_page_id):
        order_val = len_page - index  # order 值
        BookPages.objects.filter(pk=page_id).update(order=order_val)

    # ———— 刷新 book 缓存 ————
    Books.fresh_cache(book_id)

    return JsonResponse(out.send(msg='排序成功'))


def api_book_download(request, book_id):
    """书本-下载"""

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        return JsonResponse(out.QUERY_ERROR_OF_ID_NOT_EXISTING)

    # 执行下载操作，打包 zip
    zip_out_path, out_zip_name = db_book.download_book()

    # 作为附件 返回
    f = open(zip_out_path, 'rb')
    resp = FileResponse(f, as_attachment=True, filename=out_zip_name)
    resp['Content-Type'] = 'application/octet-stream'
    resp['Content-Disposition'] = f"attachment; filename*=UTF-8''{out_zip_name}"

    # 移除 zip 文件
    try:
        os.remove(zip_out_path)
    except BaseException:  # window系统可能无法移除
        pass

    return resp


def api_books_title_update(request, book_id):
    """书本-修改-标题"""

    jo = json.loads(request.body)
    title = jo.get("title", "")

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        return JsonResponse({'code': 1, 'msg': '书本不存在'})

    db_book.title = title
    db_book.save()

    # ==== 刷新 book 缓存 ====
    Books.fresh_cache(book_id)

    return JsonResponse({'code': 0, 'msg': '修改成功'})


def api_books_collect(request, book_id):
    """书本-切换收藏"""

    if BookCollections.objects.filter(book_id=book_id).first():  # 已存在，则取消
        BookCollections.objects.filter(book_id=book_id).delete()
        tag = 0
    else:  # 不存在，则添加
        BookCollections.objects.create(book_id=book_id)
        tag = 1

    # ==== 刷新 book 缓存 ====
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "成功", "data": tag})


def api_books_genre(request, book_id):
    """书本-修改-类型"""
    jo = json.loads(request.body)
    genre = jo.get("genre", "")
    if not genre:
        return JsonResponse({"code": 1, "msg": "类型不能为空"})

    # 查询 genre
    db_genre = Genres.objects.filter(value=genre).first()
    if not db_genre:
        return JsonResponse({"code": 1, "msg": "类型不存在"})

    Books.objects.filter(id=book_id).update(genre=db_genre)

    # ==== 刷新 book 缓存 ====
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "成功"})


def api_books_tags_update(request, book_id):
    """
    修改-书本-标签
    """

    jo = json.loads(request.body)
    li_tag_id = jo.get('li_tag_id', [])

    # 批量查询 db tags
    li_db_tags = Tags.objects.filter(id__in=li_tag_id)

    # book
    db_book = Books.objects.filter(id=book_id).first()
    if not db_book:
        return JsonResponse({'code': 1, 'msg': 'book id不存在'})

    # 批量修改
    db_book.tags.set(li_db_tags)

    # ==== 刷新 book 缓存 ====
    Books.fresh_cache(book_id)

    return JsonResponse({'code': 0})


def api_books_subjects_update(request, book_id):
    """
    修改-书本-专题
    """

    jo = json.loads(request.body)
    li_subject_id = jo.get('li_subject_id', [])

    # 找出所有 新修改的subject_id
    li_new_subject_id = Subject.objects.filter(
        id__in=li_subject_id).values_list("id", flat=True)

    # 找出 book 绑定的所有 subject
    li_db_link = SubjectBookLink.objects.filter(book_id=book_id).all()

    index = 0
    while 1:
        tag_db = index < len(li_db_link)
        tag_data = index < len(li_new_subject_id)

        if tag_db and tag_data:  # 修改
            db_item = li_db_link[index]
            db_item.subject_id = li_new_subject_id[index]
            db_item.save()
        elif not tag_db and tag_data:  # 新增
            SubjectBookLink.objects.create(
                book_id=book_id,
                subject_id=li_new_subject_id[index]
            )
        elif tag_db and not tag_data:  # 删除
            SubjectBookLink.objects.filter(
                book_id=book_id, subject_id=li_db_link[index].subject_id).delete()
        else:
            break
        index += 1

    # 刷新 book 缓存
    Books.fresh_cache(book_id)

    # 刷新 subject 缓存
    for sid in li_new_subject_id:
        SubjectBookLink.fresh_cache(sid)

    return JsonResponse({'code': 0})
    Books.fresh_cache(book_id)

    # 刷新 subject 缓存
    for sid in li_new_subject_id:
        SubjectBookLink.fresh_cache(sid)

    return JsonResponse({'code': 0})
