"""
URL configuration for dj_my_steward project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path

from media_station import views, views_subject

urlpatterns = [
    path('', views.index, name='index'),  # 首页
    path('index/', views.index, name='index'),  # 首页

    path('book/<book_id>/', views.book_get),  # 图册-详情
    path('book/<book_id>/del', views.book_del),  # 图册-删除
    path('upload/', views.upload, name='upload'),  # 上传

    path('aj_collect/', views.aj_collect),  # 异步-图册-收藏
    path('aj_book/list/', views.aj_book_list),  # 异步-图册-列表
    path('aj_recommend_list/', views.aj_recommend_list),  # 异步-推荐-列表
    path('aj_book/get/', views.aj_book_get),  # 异步请求-详情
    path('aj_batch_update_tags/', views.aj_batch_update_tags),  # 异步请求-修改标签
    path('aj_batch_set_genre/', views.aj_batch_set_genre),  # 异步请求-批量设置类型
    path('aj_batch_bind_tags/', views.aj_batch_bind_tags),  # 异步请求-批量绑定标签

    path('subjects/', views_subject.subjects, name='subjects'),  # 专题-列表
    path('subjects/add/', views_subject.add_subjects),  # 专题-添加
    path('subjects/<subject_id>/', views_subject.subject_detail),  # 专题-详情

    path('aj_subject/list/', views_subject.aj_subject_list),  # 异步-专题列表
    path('aj_subject/all_sort/', views_subject.aj_subject_all_sort),  # 专题-全部重新排序
    path('aj_subject/<subject_id>/asc_priority/update/', views_subject.aj_subject_asc_priority_update),  # 异步-专题-排序-更新
    path('aj_subject/<subject_id>/book/list/', views_subject.aj_subject_book_list),  # 异步-专题-所有图册-列表
    path('aj_subject/<subject_id>/book/batch_add/', views_subject.aj_subject_book_batch_add),  # 专题-图册-批量添加
    path('aj_subject/<subject_id>/book/<book_id>/del/', views_subject.aj_subject_book_del),  # 专题-图册-删除
    path('aj_subject/<subject_id>/book/all_sort/', views_subject.aj_subject_book_all_sort),  # 专题-图册-全部重新排序
    # 异步-专题-图册-排序-更新
    path('aj_subject/<subject_id>/book/<book_id>/asc_priority/update/',
         views_subject.aj_subject_book_asc_priority_update),


    # ———— v3 升级 ————
    path('v3/', views.index_v3, name='index_v3'),  # 首页
    path('v3/api/tag/add/', views.v3_api_tag_add),  # api 添
    path('v3/api/books/', views.v3_api_books_list),  # api 书本-列表
    path('v3/api/book-genres/', views.v3_api_book_genres),
    path('v3/api/book-tags/', views.v3_api_book_tags),
    path('v3/book/<book_id>/', views.book_get_v3, name='book_v3'),  # 书本-详情
    path('v3/api/book/merge/', views.v3_api_book_merge),  # 合并
    path('v3/api/book/<book_id>/title/update/', views.v3_api_book_title_update),  # 修改 book title# 修改 book title
    path('v3/api/book/<book_id>/del/', views.v3_api_book_del),  # 书本-删除
    path('v3/api/book/<book_id>/download/', views.v3_api_book_download),  # 书本-下载
    path('v3/api/book/<book_id>/page/<page_id>/del/', views.v3_api_book_page_del),  # 删除page
    path('v3/api/book/<book_id>/page/all_sort/', views.v3_api_book_page_all_sort),  # book 图片重新排序
    path('v3/api/book-subjects/', views.v3_api_book_subjects),
    path('v3/api/book/<book_id>/genre/update/', views.v3_api_book_genre_update),  # 修改 book类型
    path('v3/api/book/<book_id>/tags/update/', views.v3_api_book_tags_update),  # 修改 book标签
    path('v3/api/book/<book_id>/subjects/update/', views.v3_api_book_subjects_update),

    path('v3/subjects/', views_subject.subjects_v3, name='subjects_v3'),  # 专题
    path('v3/subject/<subject_id>/', views_subject.subject_v3_detail, name='subject_v3_detail'),  # 专题-详情
    path("v3/api/subject/add/", views_subject.v3_api_subject_add),  # 添加专题
    path('v3/api/subject/all_sort/', views_subject.v3_api_subject_all_sort),  # 专题-全部重新排序
    path('v3/api/subject/<subject_id>/book/list/', views_subject.v3_api_subject_book_list),  # 专题-图册-列表   
    path('v3/api/subject/<subject_id>/books/sort/', views_subject.v3_api_subject_details_book_sort),  # 专题-详情-图册-排序
]
