import json
from django.contrib import messages
from django.core.cache import cache
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.views.decorators.csrf import csrf_exempt

from media_station.models import Subject, Books, SubjectBookLink
from media_station.views import get_genres, get_tags


@csrf_exempt
def subjects(request):
    """专题"""

    return render(request,
                  'media_station/subjects.html',
                  context={
                      'li_genres': get_genres(),  # 类型列表
                  })


def subjects_v3(request):
    """专题-v3"""

    li_subjects = Subject.objects.order_by('asc_priority', '-created_at').all()

    return render(request,
                  'media_station/v3/subject_v3.html',
                  context={
                      'li_genres': get_genres(),  # 类型列表
                      'li_subjects': li_subjects,
                  })


def subject_v3_detail(request, subject_id):
    """专题-v3-详情"""

    subject = Subject.objects.filter(id=subject_id).first()

    return render(request,
                  'media_station/v3/subject_details_v3.html',
                  context={
                      'subject': subject,
                      'li_genres': get_genres(),  # 类型列表
                  })


def add_subjects(request):
    """专题-添加"""

    if request.method == 'GET':
        return render(request, 'media_station/subjects_add.html')

    # 提交
    title = request.POST.get('title')

    # 判重复
    if Subject.objects.filter(title=title).first():
        messages.error(request, '标题重复')

    # 添加
    Subject.objects.create(title=title)
    messages.success(request, '添加专题成功')
    return redirect('/subjects')


@csrf_exempt
def subject_detail(request, subject_id):
    """专题-详情"""
    subject = Subject.objects.filter(id=subject_id).first()

    return render(request,
                  'media_station/subjects_details.html',
                  context={
                      'subject': subject,
                      'li_genres': get_genres(),  # 类型列表
                      'li_tags': get_tags(),  # 类型列表
                  })


@csrf_exempt
def aj_subject_list(request):
    """专题-列表"""

    values = request.GET

    key = values.get('key')
    page = int(values.get('page', 1))
    size = int(values.get('size', 50))

    start = (page - 1) * size

    res = Subject.objects

    # 关键词
    if key:
        res = res.filter(
            title__icontains=key
        )

    # 按照 权重排序
    order_block = 'asc_priority', '-created_at'

    return JsonResponse(
        {
            'code': 0,
            'data': {
                'list': [item.subject_info() for item in res.order_by(*order_block)[start:start + size]],
                'count': res.count(),
                'size': size
            }
        }
    )


@csrf_exempt
def aj_subject_asc_priority_update(request, subject_id):
    """专题-排序-修改"""

    val = request.POST['asc_priority']

    Subject.objects.filter(
        id=subject_id
    ).update(
        asc_priority=val
    )

    return JsonResponse(
        {
            'code': 0
        }
    )


@csrf_exempt
def aj_subject_book_list(request, subject_id):
    """专题-所有图册-列表"""

    c_key = SubjectBookLink.gen_cache_key(subject_id)

    # 查看缓存
    li_out = cache.get(c_key)

    if not li_out:  # 不存在，查询
        res = Books.objects.filter(
            is_visible=True,
            subjectbooklink__subject_id=subject_id  # 筛选 专题id
        ).order_by(
            'subjectbooklink__asc_priority',
            'title'
        ).all()

        li_out = [
            {
                **item.book_info(),
                # 专题中的 图册权重
                'asc_priority':
                    SubjectBookLink.objects.filter(
                        subject_id=subject_id, book_id=item.id
                    ).values('asc_priority').first()['asc_priority']
            }
            for item in res
        ]

        # 保存到缓存
        cache.set(c_key, li_out)

    return JsonResponse(
        {
            'code': 0,
            'data': li_out
        }
    )


@csrf_exempt
def aj_subject_book_del(request, subject_id, book_id):
    """专题-图册-删除"""

    # 删除 关联
    SubjectBookLink.objects.filter(
        subject_id=subject_id,
        book_id=book_id
    ).delete()

    # 更新 subject 缓存
    SubjectBookLink.fresh_cache(subject_id)
    # 更新 book 缓存
    Books.fresh_cache(book_id)

    return JsonResponse(
        {
            'code': 0
        }
    )


@csrf_exempt
def aj_subject_all_sort(request):
    """专题-全部排序"""

    li_subject_id_with_new_sort = request.POST.getlist(
        'li_subject_id_with_new_sort[]', [])

    # 循环更新 asc_priority
    for index, subject_id in enumerate(li_subject_id_with_new_sort):
        Subject.objects.filter(
            id=subject_id
        ).update(
            asc_priority=index + 1  # 按照 index顺序更新
        )

    return JsonResponse(
        {'code': 0}
    )


@csrf_exempt
def aj_subject_book_all_sort(request, subject_id):
    """
    专题-图册-全部排序
    """

    li_book_id_with_new_sort = request.POST.getlist(
        'li_book_id_with_new_sort[]', [])

    # 循环更新 asc_priority
    for index, book_id in enumerate(li_book_id_with_new_sort):
        SubjectBookLink.objects.filter(
            subject_id=subject_id,
            book_id=book_id
        ).update(
            asc_priority=index + 1  # 按照 index顺序更新
        )

    # 更新缓存
    SubjectBookLink.fresh_cache(subject_id)

    return JsonResponse(
        {'code': 0}
    )


@csrf_exempt
def aj_subject_book_asc_priority_update(request, subject_id, book_id):
    """异步-专题-图册-排序-更新"""
    val = request.POST['asc_priority']

    SubjectBookLink.objects.filter(
        subject_id=subject_id,
        book_id=book_id
    ).update(
        asc_priority=val
    )

    # 更新缓存
    SubjectBookLink.fresh_cache(subject_id)

    return JsonResponse(
        {
            'code': 0
        }
    )


@csrf_exempt
def aj_subject_book_batch_add(request, subject_id):
    """专题-图册-批量添加"""

    li_book_id = request.POST.getlist('li_book_id[]', [])

    # 找出 已经绑定过的 book_id
    li_has_bind_book_id = SubjectBookLink.objects.filter(
        subject_id=subject_id
    ).values_list('book_id', flat=True)

    # 批量添加
    for book_id in li_book_id:
        book_id = int(book_id)
        if book_id not in li_has_bind_book_id:  # 没绑定过，才添加
            SubjectBookLink.objects.create(
                subject_id=subject_id,
                book_id=book_id
            )

            # 刷新 Book缓存
            Books.fresh_cache(book_id)

    # 刷新 subject缓存
    SubjectBookLink.fresh_cache(subject_id)

    return JsonResponse(
        {'code': 0}
    )


def v3_api_subject_add(request):
    """专题-添加"""

    subject_title = request.POST.get('title')

    # 添加
    Subject.objects.create(
        title=subject_title
    )

    return JsonResponse({'code': 0, 'msg': '添加成功'})


def v3_api_subject_all_sort(request):
    """专题-全部重新排序"""

    li_subject_id_with_new_sort = json.loads(request.body)['li_subject_id']

    # 循环更新 asc_priority
    for index, subject_id in enumerate(li_subject_id_with_new_sort):
        Subject.objects.filter(
            id=subject_id
        ).update(
            asc_priority=index + 1  # 按照 index顺序更新
        )

    return JsonResponse({'code': 0, 'msg': '排序成功'})


def v3_api_subject_book_list(request, subject_id):
    """专题-图册-列表"""

    c_key = SubjectBookLink.gen_cache_key(subject_id)

    # 查看缓存
    li_out = cache.get(c_key)

    if not li_out:  # 不存在，查询
        res = Books.objects.filter(
            is_visible=True,
            subjectbooklink__subject_id=subject_id  # 筛选 专题id
        ).order_by(
            'subjectbooklink__asc_priority',
            'title'
        ).all()

        li_out = [
            {
                **item.book_info(),
                # 专题中的 图册权重
                'asc_priority':
                    SubjectBookLink.objects.filter(
                        subject_id=subject_id, book_id=item.id
                    ).values('asc_priority').first()['asc_priority']
            }
            for item in res
        ]

        # 保存到缓存
        cache.set(c_key, li_out)

    return JsonResponse(
        {
            'code': 0,
            'data': li_out
        }
    )


def v3_api_subject_details_book_sort(request, subject_id):
    """
    专题-详情-图册-排序
    """

    li_book_id = json.loads(request.body)['book_ids']

    # 循环更新 asc_priority
    for index, book_id in enumerate(li_book_id):
        SubjectBookLink.objects.filter(
            subject_id=subject_id,
            book_id=book_id
        ).update(
            asc_priority=index + 1  # 按照 index顺序更新
        )

    # 更新缓存
    SubjectBookLink.fresh_cache(subject_id)

    return JsonResponse(
        {'code': 0}
    )
