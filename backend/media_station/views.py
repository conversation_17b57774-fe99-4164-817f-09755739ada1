import json
import math
import os
import re
import shutil

from django.contrib import messages
from django.db.models import Q
from django.http import FileResponse, Http404, JsonResponse
from django.shortcuts import render, redirect
# Create your views here.
from django.views.decorators.cache import cache_page
from django.views.decorators.csrf import csrf_exempt

from main.settings import MEDIA_ROOT
from media_station.constants import RESOURCE_SUB_DIR
from media_station.models import (
    Books,
    BookPages,
    Genres,
    Tags,
    BookCollections,
    Subject,
    SubjectBookLink,
)
from utils.easy_file import EasyFile
from utils.easy_image import EasyImage


def get_genres():
    return Genres.objects.order_by("value").all()


def get_tags():
    return Tags.objects.order_by("value").all()


@csrf_exempt
@cache_page(60 * 10)
def index(req):
    """首页"""

    if not req.user.is_authenticated:  # no login，404
        raise Http404

    return render(
        req,
        "media_station/index.html",
        context={
            "li_genres": get_genres(),  # 类型列表
            "li_tags": get_tags(),  # 类型列表
            "li_subjects": Subject.objects.all(),  # 专题列表
        },
    )


@csrf_exempt
def aj_collect(req):
    """
    异步-收藏-图册
    """

    book_id = int(req.POST.get("id"))

    if BookCollections.objects.filter(book_id=book_id).first():  # 已存在，则取消
        BookCollections.objects.filter(book_id=book_id).delete()
        tag = 0
    else:  # 不存在，则添加
        BookCollections.objects.create(book_id=book_id)
        tag = 1

    # ==== 刷新 book 缓存 ====
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "成功", "data": tag})


@csrf_exempt
@cache_page(60 * 3)
def aj_book_get(request):
    """异步请求 详情"""

    book_id = request.GET.get("id")

    db_book = Books.objects.filter(pk=book_id).first()

    return JsonResponse(
        {
            "code": 0,
            "data": db_book.book_info(),  # 图册 基本信息，不带 page 和 同subject 信息
        }
    )


@csrf_exempt
def aj_recommend_list(request):
    values = request.GET

    book_id = values.get("book_id", 0)

    db_book = Books.objects.filter(id=book_id).first()
    li_recommend_books = []
    if db_book:
        li_recommend_books = Books.objects.filter(
            genre_id=db_book.genre_id,  # 筛选 同类型
            is_visible=True,  # 筛选 开启
        ).order_by("-order", "title")[:20]

    return JsonResponse(
        {
            "code": 0,
            "data": {
                "list": [item.book_info() for item in li_recommend_books],
            },
        }
    )


@csrf_exempt
def aj_book_list(req):
    """
    异步-图册-列表
    """

    value = req.GET

    key = req.GET.get("key")
    page = int(req.GET.get("page", 1))
    size = int(req.GET.get("size", 50))
    genre = req.GET.get("genre")
    li_tag = req.GET.getlist("li_tags[]", [])  # aa;bb;cc
    is_collect = int(req.GET.get("is_collect", 0))
    no_genre = int(req.GET.get("no_genre", 0))
    no_tags = int(req.GET.get("no_tags", 0))
    no_subject = int(req.GET.get("no_subject", 0))
    order_by_views_asc = int(req.GET.get("order_by_views_asc", 0))
    order_by_created_at_asc = int(req.GET.get("order_by_created_at_asc", 0))
    subject_id = int(value.get("subject_id", 0))  # 通过 专题进行筛选
    ignore_by_subject_id = int(
        value.get("ignore_by_subject_id", 0))  # 过滤专题下的图册
    only_subject = int(value.get("only_subject", -1))  # 仅专题
    is_random = int(value.get("is_random", 0))  # 随机

    start = (page - 1) * size

    res = Books.objects.filter(
        # 筛选 可见
        is_visible=True,
    )

    order_block = ("-id",)

    # 关键词搜索
    if key:
        res = res.filter(title__icontains=key)

    # 筛选 类型
    if genre:
        db_genre = Genres.objects.filter(value=genre).first()
        if db_genre:
            res = res.filter(genre_id=db_genre.id)
            order_block = "-order", "title"

    # 筛选 标签
    if li_tag:
        for tag_value in li_tag:
            res = res.filter(tags__value=tag_value)

    # 筛选 收藏
    if is_collect:  # 已收藏
        res = res.filter(bookcollections__isnull=False)

    # 筛选 无类型
    if no_genre:
        res = res.filter(genre_id__isnull=True)

    # 筛选 无标签
    if no_tags:
        res = res.filter(tags__isnull=True)

    # 筛选 无专题
    if no_subject:
        res = res.filter(subjectbooklink__isnull=True)

    # 仅专题
    if only_subject in (0, 1):
        # 筛选 非仅专题可见
        res = res.filter(only_subject=not not only_subject)

    # 筛选 专题
    if subject_id:
        li_match_book_id = list(
            SubjectBookLink.objects.filter(subject_id=subject_id).values_list(
                "book_id", flat=True
            )
        )
        res = res.filter(id__in=li_match_book_id)

    # 过滤 专题
    if ignore_by_subject_id:
        li_ignore_book_id = list(
            SubjectBookLink.objects.filter(subject_id=ignore_by_subject_id).values_list(
                "book_id", flat=True
            )
        )
        res = res.filter(~Q(id__in=li_ignore_book_id))

    # ———— 排序 ————
    if order_by_views_asc:
        order_block = ("views",)

    if order_by_created_at_asc:
        order_block = ("created_at",)

    if is_random:
        li_out = [item.book_info() for item in res.order_by("?")[:size]]
    else:
        li_out = [
            item.book_info()
            for item in res.order_by(*order_block)[start: start + size]
        ]

    return JsonResponse(
        {"code": 0, "data": {"list": li_out, "count": res.count(), "size": size}}
    )


def book_del(req, book_id):
    """
    删除-book
    """

    if not req.user.is_authenticated:  # no login，404
        raise Http404

    db_book = Books.objects.filter(pk=book_id).first()
    dir_name = db_book.get_book_save_dir_name()  # 获取文件夹

    Books.objects.filter(pk=book_id).delete()  # 删除 db

    # 删除 资源文件
    if dir_name:
        Books.delete_dir_with_title(dir_name)

    return redirect("/")


@csrf_exempt
def book_get(req, book_id):
    """图册-详情"""

    if not req.user.is_authenticated:  # no login，404
        raise Http404

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        messages.error(req, f"book: {book_id} 不存在")
        return redirect("/")

    db_book.views += 1
    db_book.save()

    # 找出所有专题
    li_subject_book = []
    li_subject_of_book = (
        SubjectBookLink.objects.filter(
            book_id=book_id  # 筛选 当前 图册，找出所有绑定了的专题
        )
            .order_by("subject__asc_priority")
            .all()
    )
    for subject_link in li_subject_of_book:  # 遍历所有专题
        # 获取 当前专题的所有 book
        subject_id = subject_link.subject_id

        li_same_subject_book_link_list = (
            Books.objects.filter(
                is_visible=True, subjectbooklink__subject_id=subject_id  # 筛选 专题id
            )
                .order_by("subjectbooklink__asc_priority", "title")
                .all()
        )

        li_subject_book.append(
            {
                "id": subject_link.subject.id,
                "title": subject_link.subject.title,
                "li_book": [
                    {
                        "id": book.id,
                        "title": book.title,
                        # 当前书本在此专题中的排序
                        "asc_priority": SubjectBookLink.objects.filter(
                            subject_id=subject_id, book_id=book.id
                        ).values("asc_priority").first()["asc_priority"],
                        "thumb_url": book.get_book_first_img(),
                    }
                    for book in li_same_subject_book_link_list
                ],
            }
        )

    # 如果只属于特定专题，则 获取 上一本，下一本
    pre_link = next_link = ""
    tmp_subject = SubjectBookLink.objects.filter(book_id=book_id).all()
    if len(tmp_subject) == 1:  # 只有一个专题
        db_subject_book_link = tmp_subject[0]

        subject_id = db_subject_book_link.subject_id  # 所属 专题id
        book_priority_on_this_subject = (
            db_subject_book_link.asc_priority
        )  # 在专题中的排序

        # 上一本
        pre_book = (
            SubjectBookLink.objects.filter(
                subject_id=subject_id, asc_priority__lt=book_priority_on_this_subject
            )
                .order_by("-asc_priority")
                .first()
        )
        if pre_book:
            pre_link = f"/book/{pre_book.book_id}"

        # 下一本
        next_book = (
            SubjectBookLink.objects.filter(
                subject_id=subject_id, asc_priority__gt=book_priority_on_this_subject
            )
                .order_by("asc_priority")
                .first()
        )
        if next_book:
            next_link = f"/book/{next_book.book_id}"

    return render(
        req,
        "media_station/book.html",
        context={
            "book_id": int(book_id),
            "li_genres": get_genres(),  # 类型列表
            "li_tags": get_tags(),  # 类型列表
            # 页图片
            "li_image_page": db_book.bookpages_set.order_by("-order", "page"),
            # 所属专题信息
            "li_subject_book": li_subject_book,
            # 只属于一个专题，才有效
            "pre_link": pre_link,
            "next_link": next_link,
        },
    )


@csrf_exempt
def upload(req):
    """上传"""

    if not req.user.is_authenticated:  # no login，404
        raise Http404

    # ———— 页面 ————
    if req.method == "GET":
        return render(
            req,
            "media_station/upload.html",
            context={
                "li_genres": get_genres(),  # 类型列表
            },
        )

    # ———— 上传操作 ————
    title = req.POST.get("title")
    zip_file = req.FILES.get("zip_file")

    if not zip_file:
        messages.error(req, "文件为空")
        return redirect("/upload")
    if not str(zip_file.name).endswith(".zip"):
        messages.error(req, "文件格式不支持")
        return redirect("/upload")

    zip_file_name = str(zip_file.name).rsplit(".", 1)[0]

    if not title:  # 没有title，则使用文件名
        title = zip_file_name

    if Books.objects.filter(title=title).first():
        messages.error(req, f"标题已存在：{title}")
        return redirect("/upload")

    # ———— 保存临时文件 ————
    # 临时文件夹
    tmp_dir = os.path.join(MEDIA_ROOT, "tmp")
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)

    zip_file_path = os.path.join(tmp_dir, zip_file.name)  # 临时文件路径
    # 保存到本地
    with open(zip_file_path, "wb+") as f:
        for chunk in zip_file.chunks():
            f.write(chunk)

    # ———— 解压 zip ————
    # 解压的目标文件夹
    easy_file = EasyFile()
    de_dir_path = easy_file.decompress(zip_file_path)  # 解压
    # print('解压后位置：', de_dir_path)

    # 调整解压文件夹名
    #   解压文件名 == title，则正常
    #   解压文件名 != title，则改为 title
    if os.path.basename(de_dir_path) != title:  # 提取 文件夹名字 来判断
        tmp_dir_with_title = os.path.join(
            os.path.dirname(de_dir_path), title  # 提取解压文件夹的 上一层
        )
        # 改名
        shutil.move(
            de_dir_path,  # 原始待修改的文件名路径
            tmp_dir_with_title,  # 改为title后的文件名路径
        )
        de_dir_path = tmp_dir_with_title

    # 创建新的实际文件夹
    target_save_path = os.path.join(
        MEDIA_ROOT, RESOURCE_SUB_DIR, title
    )  # 实际文件夹保存文件

    # 移动 临时文件夹到真实文件夹
    shutil.move(de_dir_path, target_save_path)
    print("实际位置：", target_save_path)

    # ———— 保存 整本书到db ————
    db_image = Books(title=title, is_visible=True)
    db_image.save()

    # 遍历文件夹，保存每一页到db
    total_page = 0

    easy_img = EasyImage()

    for file_name in sorted(os.listdir(target_save_path)):
        if file_name.startswith("."):  # 忽略隐藏文件
            continue

        # 处理 webp
        if file_name.endswith(".webp"):  # webp格式，直接转换为 jpg格式
            # webp 转 jpg
            res = easy_img.webp_2_jpg(
                os.path.join(target_save_path, file_name), remove_origin=True
            )
            file_name = os.path.basename(res)  # 重置 filename，后续可以上传

        if any(
                [
                    file_name.endswith(".jpg"),
                    file_name.endswith(".jpeg"),
                    file_name.endswith(".png"),
                ]
        ):
            # 保存到每一页到db

            add_jo = {
                "image": db_image,
                # 保存格式：image_books/标题/图片文件名
                "img_path": f"{RESOURCE_SUB_DIR}/{title}/{file_name}",
            }
            page_str = file_name.rsplit(".", 1)[0]
            if page_str.isdigit():  # 标题就是数字，则直接认为是页码
                add_jo["page"] = page_str

            db_image_page = BookPages(**add_jo)
            db_image_page.save()
            total_page += 1

    # 设置 页数
    db_image.page_count = total_page
    db_image.save()

    # ———— 删除临时zip文件 ————
    if os.path.exists(zip_file_path):
        os.remove(zip_file_path)

    messages.success(req, "上传成功")
    return redirect("/upload")


@csrf_exempt
def aj_batch_bind_tags(req):
    li_books_id = req.POST.getlist("li_books_id[]")
    li_tags = req.POST.getlist("li_tags[]")

    li_db_tags = [Tags.objects.filter(value=tag_value).first()
                  for tag_value in li_tags]

    # 遍历每本书，循环绑定标签
    for book_id in li_books_id:
        db_book = Books.objects.filter(pk=book_id).first()
        if not db_book:
            continue

        # 遍历 标签
        for db_tag in li_db_tags:
            # 存在，则忽略
            if db_book.tags.filter(value=db_tag.value).first():
                continue

            # 添加
            db_book.tags.add(db_tag)

        # ==== 刷新 book 缓存 ====
        Books.fresh_cache(book_id)

    return JsonResponse({"code": 0})


@csrf_exempt
def aj_batch_update_tags(req):
    """修改标签"""
    li_books_id = req.POST.getlist("li_books_id[]")
    li_tags = req.POST.getlist("li_tags[]")

    li_db_tags = [Tags.objects.filter(value=tag_value).first()
                  for tag_value in li_tags]

    # 遍历每本书，循环绑定标签
    for book_id in li_books_id:
        db_book = Books.objects.filter(pk=book_id).first()
        if not db_book:
            continue

        # 直接 set 修改新的标签
        db_book.tags.set(li_db_tags)

        # ==== 刷新 book 缓存 ====
        Books.fresh_cache(book_id)

    return JsonResponse({"code": 0})


@csrf_exempt
def aj_batch_set_genre(req):
    li_books_id = req.POST.getlist("li_books_id[]")
    genre_value = req.POST.get("genre")

    db_genre = Genres.objects.filter(value=genre_value).first()

    # 遍历每本书，循环绑定标签
    for book_id in li_books_id:
        Books.objects.filter(pk=book_id).update(genre=db_genre)

        # ==== 刷新 book 缓存 ====
        Books.fresh_cache(book_id)

    return JsonResponse({"code": 0})




@csrf_exempt
def index_v3(request):
    li_genres = Genres.objects.order_by("value").all()
    li_tags = Tags.objects.order_by("value").all()

    return render(request,
                  "media_station/v3/index_v3.html",
                  context={
                      'li_genres': [
                          {
                              "id": genre.id,
                              "value": genre.value,
                              "count": genre.get_book_count()
                          }
                          for genre in li_genres
                      ],
                      'li_tags': [
                          tag.get_tag_info()
                          for tag in li_tags
                      ]
                  })


def v3_api_tag_add(request):
    """api 添加标签"""

    tag = request.POST.get("tag")

    Tags.objects.create(value=tag)

    return JsonResponse({"code": 0, "msg": "添加标签成功"})


def book_get_v3(request, book_id):
    """v3 书本-详情"""
    if not request.user.is_authenticated:  # no login，404
        raise Http404

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        messages.error(request, f"book: {book_id} 不存在")
        return redirect("/")

    db_book.views += 1
    db_book.save()

    # 找出所有专题
    li_subject_book = []
    li_subject_of_book = (
        SubjectBookLink.objects.filter(
            book_id=book_id  # 筛选 当前 图册，找出所有绑定了的专题
        )
            .order_by("subject__asc_priority")
            .all()
    )
    for subject_link in li_subject_of_book:  # 遍历所有专题
        # 获取 当前专题的所有 book
        subject_id = subject_link.subject_id

        li_same_subject_book_link_list = (
            Books.objects.filter(
                is_visible=True, subjectbooklink__subject_id=subject_id  # 筛选 专题id
            )
                .order_by("subjectbooklink__asc_priority", "title")
                .all()
        )

        li_subject_book.append(
            {
                "id": subject_link.subject.id,
                "title": subject_link.subject.title,
                "li_book": [
                    {
                        "id": book.id,
                        "title": book.title,
                        # 当前书本在此专题中的排序
                        "asc_priority": SubjectBookLink.objects.filter(
                            subject_id=subject_id, book_id=book.id
                        )
                            .values("asc_priority")
                            .first()["asc_priority"],
                        "thumb_url": book.get_book_first_img(),
                    }
                    for book in li_same_subject_book_link_list
                ],
            }
        )

    # 如果只属于特定专题，则 获取 上一本，下一本
    pre_link = next_link = ""
    tmp_subject = SubjectBookLink.objects.filter(book_id=book_id).all()
    if len(tmp_subject) == 1:  # 只有一个专题
        db_subject_book_link = tmp_subject[0]

        subject_id = db_subject_book_link.subject_id  # 所属 专题id
        book_priority_on_this_subject = (
            db_subject_book_link.asc_priority
        )  # 在专题中的排序

        # 上一本
        pre_book = (
            SubjectBookLink.objects.filter(
                subject_id=subject_id, asc_priority__lt=book_priority_on_this_subject
            )
                .order_by("-asc_priority")
                .first()
        )
        if pre_book:
            pre_link = f"/v3/book/{pre_book.book_id}"

        # 下一本
        next_book = (
            SubjectBookLink.objects.filter(
                subject_id=subject_id, asc_priority__gt=book_priority_on_this_subject
            )
                .order_by("asc_priority")
                .first()
        )
        if next_book:
            next_link = f"/v3/book/{next_book.book_id}"

    return render(
        request,
        "media_station/v3/book_v3.html",
        context={
            "book": db_book,
            "book_id": int(book_id),
            'is_collect': db_book.get_is_collect(),
            "li_genres": get_genres(),  # 类型列表
            "li_tags": get_tags(),  # 类型列表
            # 页图片
            "li_image_page": db_book.bookpages_set.order_by("-order", "page"),
            # 所属专题信息
            "li_subject_book": li_subject_book,
            # 只属于一个专题，才有效
            "pre_link": pre_link,
            "next_link": next_link,
        },
    )

def v3_api_book_merge(request):
    """合并图书"""

    li_book_id = json.loads(request.body)["li_book_id"]

    # 获取 所有图书
    li_db_book = Books.objects.filter(pk__in=li_book_id).all()

    # 遍历所有图册，获取标题，统计总图片数量
    titles = []
    total_page = 0
    all_tags = set()    # 收集标签
    all_subjects = set()    # 收集专题
    default_genre = None    # 第一个非空类型
    is_collect = False    # 是否已收藏

    for book_item in li_db_book:
        titles.append(book_item.title)
        total_page += book_item.get_book_page_count()
            
        # 收集标签
        all_tags.update(book_item.tags.all())
        
        # 收集专题
        for subject_link in book_item.subjectbooklink_set.all():
            all_subjects.add(subject_link.subject)
            
        # 记录第一个非空的类型作为默认类型
        if not default_genre and book_item.genre:
            default_genre = book_item.genre

        # 记录是否已收藏（只要有任意一个book已收藏）
        if book_item.get_is_collect():
            is_collect = True

    # 新的文件名（也是文件夹名）
    new_title = ';'.join(titles)+'--combine'
    new_title = re.sub(r'[^a-zA-Z0-9\u4e00-\u9fa5\-\_]', '_', new_title)    # 将标题中的非法字符(不是 字母/数字/中文)替换为下划线


    # 保存路径
    target_save_path = os.path.join(
        MEDIA_ROOT,
        RESOURCE_SUB_DIR,
        new_title
    )

    # 创建文件夹
    if not os.path.exists(target_save_path):
        os.makedirs(target_save_path)

    # 创建 db book
    db_new_book = Books(
        title=new_title,
        remarks=f'合并来源：{"; ".join(titles)}',
        is_visible=True,
        genre=default_genre,  # 设置类型(有可能为空)
    )
    db_new_book.save()

    # 添加所有标签
    if all_tags:
        db_new_book.tags.set(all_tags)
    
    # 添加所有专题关联
    for subject in all_subjects:
        SubjectBookLink.objects.create(
            subject=subject,
            book=db_new_book
        )

    # 设置是否已收藏
    if is_collect:
        BookCollections.objects.create(
            book=db_new_book
        )

    # 循环复制 每本书-每一页到 新文件夹
    index_img = 0   # 图片索引
    for book_item in li_db_book:  # 遍历每本书
        for page_item in book_item.bookpages_set.order_by('-order', 'page').all():  # 遍历 书本的每一页
            img_name = f'{index_img+1}.jpg'   # 图片名称

            # 最终图片路径
            final_img_path = os.path.join(target_save_path, img_name)

            # 复制图片
            shutil.copy(page_item.img_path.path, final_img_path)

            # 保存每一页到db
            db_book_page = BookPages(
                image=db_new_book,
                img_path=f'{RESOURCE_SUB_DIR}/{new_title}/{img_name}',
                page=index_img+1, # 页码
            )
            db_book_page.save()

            index_img += 1  # 索引+1

    # 更新页数
    db_new_book.page_count = index_img
    db_new_book.save()

    return JsonResponse({"code": 0, "msg": "合并成功"})

def v3_api_book_title_update(request, book_id):
    """修改 book title"""

    title = request.POST.get("title")

    Books.objects.filter(pk=book_id).update(title=title)

    return JsonResponse({"code": 0, "msg": "修改成功"})


def v3_api_book_del(request, book_id):
    """书本-删除"""

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        return JsonResponse({"code": 1, "msg": "图书不存在"})

    dir_name = db_book.get_book_save_dir_name()  # 获取文件夹名

    # 删除 db
    db_book.delete()

    # 删除 资源文件
    if dir_name:
        Books.delete_dir_with_title(dir_name)

    return JsonResponse({"code": 0, "msg": "删除成功"})


def v3_api_book_download(request, book_id):
    """书本-下载"""

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        return JsonResponse({"code": 1, "msg": "图书不存在"})

    # 执行下载操作，打包 zip
    zip_out_path, out_zip_name = db_book.download_book()

    # 作为附件 返回
    f = open(zip_out_path, 'rb')
    resp = FileResponse(f, as_attachment=True, filename=out_zip_name)

    # 移除 zip 文件
    try:
        os.remove(zip_out_path)
    except:  # window系统可能无法移除
        pass

    return resp


@csrf_exempt
def v3_api_book_tags(request):
    li = Tags.objects.order_by("value").all()

    return JsonResponse({"code": 0, "data": [tag.get_tag_info() for tag in li]})


@csrf_exempt
def v3_api_book_genres(request):
    li = Genres.objects.order_by("value").all()

    return JsonResponse(
        {
            "code": 0,
            "data": [
                {"id": genre.id, "value": genre.value,
                 "count": genre.get_book_count()}
                for genre in li
            ],
        }
    )


@csrf_exempt
def v3_api_books_list(request):
    value = request.GET

    key = value.get("key", "")
    page = int(value.get("page", 1))
    size = int(value.get("size", 10))
    genre = value.get("genre", "")
    tags_str = value.get("tags", "")
    li_tag = [i for i in tags_str.split(";") if i.strip()]
    no_genre = int(value.get("no_genre", 0))  # 无类型
    no_tags = int(value.get("no_tags", 0))  # 无标签
    no_subject = int(value.get("no_subject", 0))  # 无专题
    is_collect = int(value.get("is_collect", 0))  # 我的收藏
    is_random = int(value.get("is_random", 0))  # 随机

    start = (page - 1) * size

    res = Books.objects.filter(
        # 筛选 可见
        is_visible=True,
    )

    order_block = ("-id",)

    # 关键词搜索
    if key:
        res = res.filter(title__icontains=key)

    # 筛选 类型
    if genre:
        db_genre = Genres.objects.filter(value=genre).first()
        if db_genre:
            res = res.filter(genre_id=db_genre.id)
            order_block = "-order", "title"

    # 筛选 标签
    if li_tag:
        for tag_value in li_tag:
            res = res.filter(tags__value=tag_value)

    # 筛选 无类型
    if no_genre:
        res = res.filter(genre_id__isnull=True)
    # 筛选 无标签
    if no_tags:
        res = res.filter(tags__isnull=True)
    # 筛选 无专题
    if no_subject:
        res = res.filter(subjectbooklink__isnull=True)
    # 筛选 我的收藏
    if is_collect:
        res = res.filter(bookcollections__isnull=False)

    if is_random:  # 随机
        li_out = [
            item.book_info()
            for item in Books.objects.filter(is_visible=True).order_by("?")[:size]
        ]
    else:  # 正常
        li_out = [
            item.book_info()
            for item in res.order_by(*order_block)[start: start + size]
        ]

    count = res.count()
    total_page = math.ceil(count / size)
    return JsonResponse(
        {
            "code": 0,
            "data": {
                "list": li_out,
                "count": count,
                "size": size,
                "total_page": total_page,
            },
        }
    )


def v3_api_book_genre_update(request, book_id):
    """修改 book 类型"""

    genre = request.POST.get("genre")  # 待修改的类型

    # 查询 genre_id
    db_genre = Genres.objects.filter(value=genre).first()
    if not db_genre:
        return JsonResponse({"code": 1, "msg": "类型不存在"})

    # 修改
    Books.objects.filter(pk=book_id).update(genre_id=db_genre.id)

    # 刷新缓存
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "修改成功"})


def v3_api_book_tags_update(request, book_id):
    """修改 book 标签"""

    li_tags = json.loads(request.body)["li_tags"]

    li_db_tags = [Tags.objects.filter(value=tag_value).first()
                  for tag_value in li_tags]

    db_book = Books.objects.filter(pk=book_id).first()
    if not db_book:
        return JsonResponse({"code": 1, "msg": "图书不存在"})

    # 直接 set 修改新的标签
    db_book.tags.set(li_db_tags)

    # ==== 刷新 book 缓存 ====
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "修改成功"})


def v3_api_book_subjects_update(request, book_id):
    """修改 book 专题"""

    li_subjects = json.loads(request.body)["li_subjects"]

    # 找出所有 新修改的subject_id
    li_new_subject_id = Subject.objects.filter(
        title__in=li_subjects).values_list("id", flat=True)

    # 找出 book 绑定的所有 subject
    li_db_link = SubjectBookLink.objects.filter(book_id=book_id).all()

    index = 0
    while 1:
        tag_db = index < len(li_db_link)
        tag_data = index < len(li_new_subject_id)

        if tag_db and tag_data:  # 修改
            db_item = li_db_link[index]
            db_item.subject_id = li_new_subject_id[index]
            db_item.save()
        elif not tag_db and tag_data:  # 新增
            SubjectBookLink.objects.create(
                book_id=book_id,
                subject_id=li_new_subject_id[index]
            )
        elif tag_db and not tag_data:  # 删除
            SubjectBookLink.objects.filter(
                book_id=book_id, subject_id=li_db_link[index].subject_id).delete()
        else:
            break
        index += 1

    # 刷新 book 缓存
    Books.fresh_cache(book_id)

    # 刷新 subject缓存
    for sid in li_new_subject_id:
        SubjectBookLink.fresh_cache(sid)

    return JsonResponse({"code": 0, "msg": "修改成功"})


def v3_api_book_subjects(request):
    """获取 book 所有专题"""

    li = Subject.objects.order_by("asc_priority").all()

    return JsonResponse({"code": 0, "data": [subject.subject_info() for subject in li]})


def v3_api_book_page_del(request, book_id, page_id):
    """删除 book 的 page"""

    # 删除 本地图片
    db_page = BookPages.objects.filter(pk=page_id).first()
    if db_page:
        os.remove(db_page.img_path.path)

    # 删除 db
    BookPages.objects.filter(pk=page_id).delete()

    # ———— 刷新 book 缓存 ————
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "删除成功"})


def v3_api_book_page_all_sort(request, book_id):
    """book 图片重新排序"""

    li_page_id = json.loads(request.body)["li_page_id"]

    len_page = len(li_page_id)

    # 按顺序更新 order，order 越大越靠前
    for index, page_id in enumerate(li_page_id):
        order_val = len_page - index  # order 值
        BookPages.objects.filter(pk=page_id).update(order=order_val)

    # ———— 刷新 book 缓存 ————
    Books.fresh_cache(book_id)

    return JsonResponse({"code": 0, "msg": "排序成功"})
