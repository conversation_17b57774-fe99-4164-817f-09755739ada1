

## 版本

node 18
python 3.8
docker


## 一键启动脚本(mac/linux)

```
./start.sh
```

## frontend

### 运行

```
cd frontend

nvm use 18

yarn dev
```

### 部署(配合nginx)

```
yarn build
```



## backend

### 开发

```
cd backend

source .venv/bin/activate

python manage.py runserver localhost:12345
```

### 部署

```
gunicorn -c gunicorn_config.py main.wsgi:application
```


## 需求

- 基础
  - [ ] 作者
  - [x] 去 element
  - [ ] 清除缓存（django tmp 文件上传，log 日志 等）

- frontend
  - [x] icon
  - [x] 折叠opr
  - [x] 导航栏优化
  - [x] icon
  - [x] title
  - [x] toast 全局支持 esc 关闭
  - [x] dialog 支持 esc 关闭
  - [x] my-dialog 改为 距离顶部固定，支持max-width,适配移动端
  - [x] 返回顶底/部
  - [ ] 首页
    - [x] 修改 类型、标签、专题
    - [x] 收藏
    - [x] 合并功能
  - [ ] 详情
    - [x] 关联专题
    - [x] 放大预览
  - [ ] 专题
    - [x] 移动端适配
  - [ ] 专题-详情
    - [x] 删除book
  - [ ] 上传
  - [x] 自动导入 组件、函数
  
- backend
  - [x] django-environ 
  - [ ] 登录返回 csrf token，解决 post csrf 403
  - [x] docker 部署下，gunicorn 启动django，会导致 cache 刷新同步问题（解决：改用redis）
  - [ ] book/info/ 与 book/detail/ 分开封装
  - [ ] 批量上传，挂载一个uploader 的volume，界面上点击上传按钮开始上传
  - [ ] admin 删除，是否同步到 volume
  - [ ] windows volume 权限问题
  - [ ] 上传功能

- 优化
  - frontend
    - [x] 考虑将 bookcard 的 修改 genre，tag，单独挪到 父级，并封装，防止多次渲染 修改逻辑代码