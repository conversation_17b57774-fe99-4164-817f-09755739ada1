user  nginx;
worker_processes  1;

events { worker_connections 1024; }

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;

    upstream backend {
        # 指向 Docker Compose的 backend容器
        server backend:12345;
    }

    server {
        listen 80;

        # ———— 开启 gzip 压缩 ————
        gzip on;
        gzip_types image/jpeg image/png image/gif text/css application/javascript;
        gzip_min_length 1000;

        # ———— 启用 keep-alive 可以减少 TCP 连接的建立次数，提升静态资源的加载速度 ————
        keepalive_timeout 65;  # 设置保持连接的时间


        # 静态文件
        location /static/ {
            alias /res/static/;
            
            # 通过设置合理的缓存头，浏览器可以缓存静态资源，减少后续请求的加载时间
            expires 30d;  # 设置缓存过期时间为 30 天
            add_header Cache-Control "public, max-age=2592000";  # 30 天的秒数
            
            # 使用 try_files 指令可以优化资源的查找效率，直接返回静态文件
            try_files $uri $uri/ =404;
        }

        # 媒体文件
        location /media/ {
            alias /res/media/;

            # 通过设置合理的缓存头，浏览器可以缓存静态资源，减少后续请求的加载时间
            expires 30d;  # 设置缓存过期时间为 30 天
            add_header Cache-Control "public, max-age=2592000";  # 30 天的秒数

            # 使用 try_files 指令可以优化资源的查找效率，直接返回静态文件
            try_files $uri $uri/ =404;
        }

        # api 请求
        location /api/ {
            # 请求将被转发到 upstream 定义的 backend服务器
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 其他请求都转发到前端
        location / {
            root /res/dist;
            try_files $uri $uri/ /index.html;
        }
    }
} 