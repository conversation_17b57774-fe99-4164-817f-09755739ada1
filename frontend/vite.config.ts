import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'


/** 
 * unplugin-auto-import: 自动导入函数和 API（<script> 中使用）
 * unplugin-vue-components: 自动导入 Vue 组件（<template> 中使用）
 */
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        // 自定义工具
        {
          // '@/utils/format': ['formatDate', 'formatPrice'],
          // '@/utils/request': ['request', 'get', 'post'],
          '@/stores': ['bookMetaStore'],
        }
      ],
      dts: 'src/auto-imports.d.ts',
    }),
    Components({
      dirs: [
        'src/components',
        'src/components/base',
        'src/views'
      ],
      dts: 'src/components.d.ts',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 使用 as * 使得变量无需前缀即可全局使用
        additionalData: `@use "@/assets/scss/variable.scss" as *;@use "@/assets/scss/global.scss" as *;`
      }
    }
  },
  server: {
    port: 5175, // 运行端口
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:12345',
        changeOrigin: true,
      },
      // 解决开发环境中，backend 的 media 文件为相对路径，无法加载的问题
      '/media': {
        target: 'http://127.0.0.1:12345',
        changeOrigin: true,
      }
    }
  },
  build: {
    minify: 'terser', // 指定使用 Terser 进行压缩
    terserOptions: {
      compress: {
        drop_console: true, // 删除 console.log
        drop_debugger: true, // 删除 debugger 语句
      },
      output: {
        comments: false, // 删除注释
      },
    },
  },
})
