<template>
  <div class="subject-details-container">
    <!-- 基本信息 -->
    <div class="subject-info">
      <!-- 固定的操作栏 -->
      <div class="fix-opr-block">
        <!-- 排序、删除 按钮 -->
        <my-button
          type="danger"
          title="编辑模式"
          size="small"
          v-show="!editMode"
          @click="toggleEditMode"
        >
          <font-awesome-icon icon="fa-solid fa-gears" />
        </my-button>
        <div v-show="editMode" class="my_flex_start">
          <my-button title="退出" size="small" @click="toggleEditMode">
            <font-awesome-icon icon="fa-solid fa-xmark" />
          </my-button>
          <my-button type="success" title="提交排序" size="small" @click="submitToSort">
            <font-awesome-icon icon="fa-solid fa-check" />
          </my-button>
        </div>
      </div>

      <!-- 信息栏 -->
      <div class="head-box">
        <div class="title">{{ subject.title }}</div>

        <!-- 收集的书本数量 -->
        <div class="book-count">
          <font-awesome-icon icon="fa-solid fa-book"></font-awesome-icon>
          {{ subject.book_count }}
        </div>

        <!-- 时间 -->
        <div class="my_small_gray">
          {{ subject.created_at }}
        </div>
      </div>

      <!-- 图片显示 -->
      <div>
        <img :src="subject.thumb_url" class="subject-img" alt="" />
      </div>
    </div>

    <!-- 专题-书本-列表 -->
    <div class="show-book-box">
      <!-- 显示图片栏 -->
      <div class="book-list" ref="sortBoxRef">
        <book-card
          v-for="book in subject.li_books"
          :key="book.id"
          :book="book"
          :data-id="book.id"
          :del-btn-visible="editMode"
          :is-simple="isMobile"
          :class="{ sorting: editMode }"
          @refresh-book="loadData"
          @ready-to-edit-tags="readyToEditTags"
          @handle-delete="handleDeleteBookFromSubject"
        />
      </div>
    </div>

    <!-- 弹窗 修改-标签 -->
    <edit-tags-dialog
      v-model:visible="editTagsVisible"
      :bookMeta="editTagsMeta"
      @on-close="() => (editTagsVisible = false)"
      @on-success="() => loadData()"
    />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { onMounted, onUnmounted, ref } from 'vue'
import { getSubjectDetailsApi, sortSubjectsBooksApi, delSubjectBookApi } from '@/api/subjects'
import type { Book, Tag } from '@/types/book'
import Sortable from 'sortablejs'
import BookCard from '@/components/BookCard.vue'

const route = useRoute()
const subjectId = route.params.subject_id as string

const isMobile = ref(false) // 是否移动端

const updateIsMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const subject = ref({
  id: 0,
  title: '',
  book_count: 0,
  thumb_url: '',
  asc_priority: 0,
  created_at: '',
  updated_at: '',
  li_books: [] as Book[],
})

// 排序-模式
const editMode = ref(false)

const sortBoxRef = ref<HTMLElement>()

// 修改-标签 数据
const editTagsVisible = ref(false)
const editTagsMeta = ref<{ id: string | number; tags: Tag[] }>({ id: 0, tags: [] })
/**
 * 准备-修改-标签
 */
const readyToEditTags = (bookMeta: Book) => {
  // 赋值
  editTagsMeta.value = {
    id: bookMeta.id,
    tags: bookMeta.tags as [],
  }

  // 打开 弹窗
  editTagsVisible.value = true
}

/**
 * 从专题移除书本
 */
const handleDeleteBookFromSubject = async (bookMeta: Book) => {
  try {
    await delSubjectBookApi(subjectId, bookMeta.id)

    // 刷新数据
    loadData()
  } catch (error) {}
}

// 切换 排序-模式
const toggleEditMode = () => {
  editMode.value = !editMode.value

  if (editMode.value && sortBoxRef.value) {
    // 进入排序模式，初始化拖动
    Sortable.create(sortBoxRef.value, {
      animation: 150,
    })
  } else {
    // 退出排序模式
    location.reload()
  }
}

/**
 * 提交-排序
 */
const submitToSort = async () => {
  try {
    const dataIds = Array.from(sortBoxRef.value?.children || []).map((el) =>
      el.getAttribute('data-id'),
    ) as []

    await sortSubjectsBooksApi(subjectId, dataIds)

    // 刷新
    location.reload()
  } catch (err) {}
}

const loadData = async () => {
  try {
    const res = await getSubjectDetailsApi(subjectId)

    subject.value = res.subject
  } catch (err) {}
}

onMounted(() => {
  updateIsMobile() // 刷新 isMobile

  loadData()

  // 监听 宽度
  window.addEventListener('resize', updateIsMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateIsMobile)
})
</script>

<style lang="scss" scoped>
.subject-details-container {
  padding: $gap;
  min-height: 400px;

  @media (max-width: $media-width) {
    padding: 0;
  }

  .subject-info {
    padding: $gap;
    position: relative;

    .fix-opr-block {
      position: absolute;
      top: $gap;
      right: $gap * 2;
      text-align: center;
      font-size: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $gap * 4;
      width: 40px;

      @media (max-width: $media-width) {
        gap: $gap * 2;
      }
    }

    @media (max-width: $media-width) {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .head-box {
      @include flex-start;
      gap: $gap * 2;
      margin-bottom: $gap;

      @media (max-width: $media-width) {
        margin-bottom: unset;
      }

      .title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .book-count {
        font-size: 12px;
        color: #999;
        margin-left: 4px;
      }
    }

    .subject-img {
      object-fit: contain;
      max-width: 200px;
      max-height: 200px;
      border-radius: 4px;
      box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 3px 0px;
    }
  }

  .show-book-box {
    // margin-top: $gap;
    border-top: 1px solid #ebebeb;
    padding: $gap;

    .book-list {
      display: flex;
      flex-wrap: wrap;
      gap: $gap * 2;

      @media (max-width: $media-width) {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
      }

      .sorting {
        cursor: move;
      }
    }
  }
}
</style>
