<template>
  <div class="upload-container">    <!-- 单文件上传示例 -->
    <div class="upload-section">
      <h3>单文件上传</h3>
      <div class="upload-box">
        <my-upload
          ref="singleUpload"
          :accept="['zip']"
          :max-size="100 * 1024 * 1024"
          @file-selected="handleSingleFileSelected"
          @file-cleared="handleSingleFileCleared"
          @error="handleError"
        />
      </div>
    </div>

    <!-- 多文件上传示例 -->
    <div class="upload-section">
      <h3>多文件上传</h3>
      <div class="upload-box">
        <my-upload
          ref="multiUpload"
          :multiple="true"
          :accept="['zip', 'rar', '7z']"
          :max-size="50 * 1024 * 1024"
          :max-files="5"
          @files-selected="handleMultiFilesSelected"
          @file-cleared="handleMultiFileCleared"
          @error="handleError"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <my-button type="danger" @click="reset">清空所有</my-button>
      <my-button type="primary" @click="submit">上传</my-button>
    </div>
  </div>
</template>

<script setup>
import { uploadBooksApi } from '@/api/book'
import { Toast } from '@/utils/toast'
import { ref } from 'vue'

// 响应式数据
const singleUpload = ref(null)
const multiUpload = ref(null)
const selectedFile = ref(null)
const selectedFiles = ref([])

// 单文件选择处理函数
const handleSingleFileSelected = (file) => {
  selectedFile.value = file
  console.log('单文件选择:', file)
}

// 单文件清空处理函数
const handleSingleFileCleared = () => {
  selectedFile.value = null
  console.log('单文件已清空')
}

// 多文件选择处理函数
const handleMultiFilesSelected = (files) => {
  selectedFiles.value = files
  console.log('多文件选择:', files)
}

// 多文件清空处理函数
const handleMultiFileCleared = () => {
  selectedFiles.value = []
  console.log('多文件已清空')
}

// 错误处理
const handleError = (error) => {
  Toast.error(error.message)
}

// 上传文件函数
const submit = async () => {
  const allFiles = []

  // 收集单文件
  if (selectedFile.value) {
    allFiles.push(selectedFile.value)
  }

  // 收集多文件
  if (selectedFiles.value.length > 0) {
    allFiles.push(...selectedFiles.value)
  }

  if (allFiles.length === 0) {
    Toast.error('请先选择文件')
    return
  }

  try {
    // 如果只有一个文件，使用单文件上传
    if (allFiles.length === 1) {
      const formData = new FormData()
      formData.append('file', allFiles[0])
      console.log('开始上传单个文件:', allFiles[0].name)
      await uploadBooksApi(formData)
      Toast.success('文件上传成功')
    } else {
      // 多文件上传，可以选择批量上传或逐个上传
      console.log('开始上传多个文件:', allFiles.map(f => f.name))

      for (const file of allFiles) {
        const formData = new FormData()
        formData.append('file', file)
        await uploadBooksApi(formData)
      }
      Toast.success(`${allFiles.length} 个文件上传成功`)
    }

    // 上传成功后清空文件
    reset()
  } catch (error) {
    console.error('上传失败:', error)
    Toast.error('上传失败：' + error.message)
  }
}

// 清空所有文件
const reset = () => {
  singleUpload.value?.clearFile()
  multiUpload.value?.clearFile()
}
</script>

<style scoped lang="scss">
.upload-container {
  padding: $gap * 4;
  margin: 0 auto;
  max-width: 800px;
}

.upload-section {
  margin-bottom: 40px;

  h3 {
    margin-bottom: 16px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  .upload-box {
    @include flex-center;
  }
}

.file-result {
  margin-top: 16px;
  padding: 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.file-result p {
  margin: 0 0 8px 0;
  color: #333;
}

.file-result button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.file-result button:hover {
  background-color: #337ecc;
}

.actions {
  text-align: center;
  margin-top: 30px;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #f56c6c;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
