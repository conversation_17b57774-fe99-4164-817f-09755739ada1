<template>
  <div class="upload-container">
    <!-- 示例1：不限制文件类型 -->
    <div class="upload-box">
      <my-upload
        ref="upload"
        @file-selected="handleFileSelected"
        @file-cleared="handleFileCleared"
        @error="handleError"
      />
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <my-button type="danger" @click="reset">清空</my-button>
      <my-button type="primary" @click="submit">上传</my-button>
    </div>
  </div>
</template>

<script setup>
import { uploadBooksApi } from '@/api/book'
import { Toast } from '@/utils/toast'
import { ref } from 'vue'

// 响应式数据
const upload = ref(null)
const selectedFile = ref(null)

// 文件选择处理函数
const handleFileSelected = (file) => {
  selectedFile.value = file
  console.log('文件选择:', file)
}

// 文件清空处理函数
const handleFileCleared = () => {
  selectedFile.value = null
  console.log('文件1已清空')
}

// 错误处理
const handleError = (error) => {
  Toast.error(error.message)
}

// 上传文件函数（模拟实际上传）
const submit = async () => {
  if (!selectedFile.value) return

  const formData = new FormData()
  formData.append('file', selectedFile.value)

  try {
    // 这里替换为你的实际上传接口
    console.log('开始上传文件:', formData)

    await uploadBooksApi(formData)
  } catch (error) {
    console.error('上传失败:', error)
    alert('上传失败：' + error.message)
  }
}

// 清空所有文件
const reset = () => {
  upload.value?.clearFile()
  // 其他上传组件也可以通过 ref 调用 clearFile 方法
}
</script>

<style scoped lang="scss">
.upload-container {
  padding: $gap * 4;
  margin: 0 auto;

  .upload-box {
    @include flex-center;
  }
}

.file-result {
  margin-top: 16px;
  padding: 12px;
  background-color: #e8f5e8;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.file-result p {
  margin: 0 0 8px 0;
  color: #333;
}

.file-result button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.file-result button:hover {
  background-color: #337ecc;
}

.actions {
  text-align: center;
  margin-top: 30px;
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #f56c6c;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
</style>
