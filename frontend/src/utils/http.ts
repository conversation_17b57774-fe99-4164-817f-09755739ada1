import axios, { type AxiosInstance, type AxiosResponse, type InternalAxiosRequestConfig } from 'axios';
import { Toast } from '@/utils/toast';

/**
 * 获取CSRF Token的辅助函数
 * @param {*} name
 * @returns
 */
function getCookie(name: string) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== "") {
    const cookies = document.cookie.split(";");
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === name + "=") {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}


const http: AxiosInstance = axios.create({
  // baseURL: "http://localhost:10001",
  baseURL: "/api",
  timeout: 5000,
  headers: {
    "X-CSRFToken": getCookie("csrftoken"), // 获取CSRF Token
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    // 只处理 HTTP 状态码为 2xx 的响应
    const jo = response.data; // 返回后端 200对应的所有 json 数据

    if (jo.code === 0) {
      // 成功，提取数据
      return jo.data;
    } else {
      // 非 code === 0 的情，逻辑错误
      Toast.error(jo.msg)
      return Promise.reject(jo.msg)
    }
  },
  async (error) => {
    // 只处理 HTTP 状态码为非 2xx 的响应（如 401、403、500 等）
    Toast.error(error.message)
    return Promise.reject(error);
  }
);

// 专用于文件下载的 Axios 实例（直接返回 Blob，不走通用 code 包装）
export const httpBinary: AxiosInstance = axios.create({
  baseURL: "/api",
  timeout: 60000,
  headers: {
    "X-CSRFToken": getCookie("csrftoken"),
  },
  responseType: 'blob',
});

httpBinary.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    return config;
  },
  (error) => Promise.reject(error)
);

httpBinary.interceptors.response.use(
  (response: AxiosResponse<Blob>) => {
    // 直接把响应返回，调用方可从 response.data 获取 Blob，并从 headers 获取文件名
    return response;
  },
  async (error) => {
    // 若后端以 JSON 错误返回且被当作 Blob，这里尝试给出提示（简化处理）
    try {
      if (error?.response?.data instanceof Blob) {
        const text = await error.response.data.text();
        // 尝试解析 JSON 错误信息
        try {
          const jo = JSON.parse(text);
          if (jo?.msg) Toast.error(jo.msg);
        } catch (_) {
          Toast.error('下载失败');
        }
      } else {
        Toast.error(error.message || '下载失败');
      }
    } catch (_) {
      Toast.error('下载失败');
    }
    return Promise.reject(error);
  }
);

export default http;
