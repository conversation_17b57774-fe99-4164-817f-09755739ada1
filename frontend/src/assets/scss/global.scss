@mixin just-1-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin just-2-line {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-end {
  display: flex;
  justify-content: flex-end;
}

@mixin flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

@mixin small-gray {
  font-size: 12px;
  color: #999999;
}

.my_flex_start {
  @include flex-start;
}

.my_flex_end {
  @include flex-end;
}

.my_small_gray {
  @include small-gray;
}
