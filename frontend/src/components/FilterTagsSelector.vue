<template>
  <!-- 标签筛选 -->
  <div class="filter-box tag-filter">
    <div class="tag-filter-header" id="tagFilterHeader" @click="toggleTagFilter">
      <font-awesome-icon icon="fa-solid fa-tags" class="tag-icon" />
      <span class="placeholder">{{ showContent }}</span>
    </div>
    <div class="tag-filter-dropdown" id="tagFilterDropdown" :class="{ active: isDropdownOpen }">
      <!-- 添加标签 -->
      <div class="add-tag">
        <font-awesome-icon
          icon="fa-solid fa-circle-plus"
          class="add-tag-btn"
          @click="() => (addTagVisible = true)"
        />
      </div>

      <div class="tag-filter-content">
        <div
          v-for="tag in liTags"
          :key="tag.id"
          class="tag-option"
          :class="{ disabled: tag.book_count === 0, selected: isSelected(tag.value) }"
          @click="handleTagClick(tag.value)"
        >
          {{ tag.value }} ({{ tag.book_count || 0 }})
        </div>
      </div>
    </div>
  </div>

  <!-- 弹窗-添加-标签 -->
  <my-dialog title="添加标签" v-model="addTagVisible" width="400px">
    <div>
      <my-input v-model="addTagValue" placeholder="标签" clear></my-input>
    </div>

    <template #footer>
      <div class="my_flex_end">
        <my-button @click="addTagVisible = false">取 消</my-button>
        <my-button type="primary" @click="submitToAddTag">确 定</my-button>
      </div>
    </template>
  </my-dialog>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount, onMounted, defineEmits, computed } from 'vue'
import { addTagApi } from '@/api/book'
import { Toast } from '@/utils/toast'
import { useBookMetaStore } from '@/stores/bookMetaStore'

const bookMetaStore = useBookMetaStore()

const props = defineProps<{
  liTags: any[] // 渲染标签
  liChoseTags: string[] // 已选中的 标签
}>()

const emit = defineEmits<{
  (e: 'update:liChoseTags', liChoseTags: string[]): void
}>()

// 下拉是否展开
const isDropdownOpen = ref(false)

// 弹窗-添加-标签
const addTagVisible = ref(false)
const addTagValue = ref<string>('')

// 显示内容
const showContent = computed(() => {
  if (props.liChoseTags.length === 0) {
    return '全部标签'
  }
  return props.liChoseTags.join(', ')
})

/**
 * 提交 添加标签
 */
const submitToAddTag = async () => {
  try {
    await addTagApi(addTagValue.value)

    Toast.success('添加成功')

    // 关闭模态框
    addTagVisible.value = false

    // 刷新 store
    bookMetaStore.fetchTags(true)
  } catch (error) {}
}

const handleTagClick = (tag: string) => {
  // 存在，则移除
  if (props.liChoseTags.some((t) => t === tag)) {
    const newChose = props.liChoseTags.filter((t) => t !== tag)
    emit('update:liChoseTags', newChose)
    return
  }

  // 不存在，添加
  const addRes = [...props.liChoseTags, tag]
  emit('update:liChoseTags', addRes)
}

/**
 * 是否选中
 */
const isSelected = (tag: string) => {
  return props.liChoseTags.some((t) => t === tag)
}

// 切换下拉菜单状态
const toggleTagFilter = (event: Event) => {
  event.stopPropagation() // 阻止事件冒泡
  isDropdownOpen.value = !isDropdownOpen.value
}

// 点击外部区域关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  const dropdown = document.querySelector('.tag-filter-dropdown')
  const header = document.querySelector('.tag-filter-header')

  if (
    dropdown &&
    header &&
    !dropdown.contains(target) &&
    !header.contains(target) &&
    isDropdownOpen.value
  ) {
    isDropdownOpen.value = false
  }
}

// 监听全局点击事件
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
$base-height: 32px;

.filter-box {
  max-width: 200px;
  position: relative;

  .tag-filter-header {
    height: $base-height;
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    background-color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;

    .tag-icon,
    .placeholder {
      color: #6a6a6a;
    }
  }

  .tag-filter-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 300px; /* 设置为header的两倍宽度 */
    margin-top: 4px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 2000; /* 增加z-index确保在book-list上方 */
    display: none;
    padding: 4px;
    max-height: 400px;
    overflow-y: auto;

    // 移动端适配
    @media (max-width: $media-width) {
      position: fixed; // 固定定位
      top: 50%; // 垂直居中
      left: 50%; // 水平居中
      transform: translate(-50%, -50%); // 完全居中
      width: 80vw; // 占据屏幕80%宽度
      max-height: 60vh; // 最大高度为屏幕60%
      z-index: 9999; // 确保在最上层
    }

    &.active {
      display: block;
    }
  }

  .tag-filter-content {
    padding: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-option {
      padding: 4px 8px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      user-select: none;

      &:hover {
        background-color: #e0e0e0;
      }

      &.selected {
        background-color: #06aa1f;
        color: white;
      }

      &.disabled {
        background-color: #f0f0f0;
        color: #999;
        cursor: not-allowed;

        opacity: 0.5;
        cursor: not-allowed;
        // background-color: #f5f5f5;
        // color: #999;
        pointer-events: none;
      }
    }
  }
}

.add-tag {
  @include flex-end;
  .add-tag-btn {
    color: $blue-light;
    font-size: 13px;
    cursor: pointer;
  }
}
</style>
