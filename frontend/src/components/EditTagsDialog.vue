<template>
  <!-- 弹窗-修改-标签 -->
  <my-dialog title="修改标签" v-model="insideVisible" :width="700" @close="close">
    <div class="edit-tags--show-block">
      <div
        class="single-tag"
        :class="{ active: liTagsIsChosing.some((tmp) => tmp.id === tag.id) }"
        v-for="tag in bookMetaStore.tags"
        :key="tag.id"
        @click="toggleEditTagChose(tag)"
      >
        {{ tag.value }}
      </div>
    </div>

    <template #footer>
      <div class="my_flex_end">
        <my-button @click="close">取 消</my-button>
        <my-button type="primary" @click="submit">确 定</my-button>
      </div>
    </template>
  </my-dialog>
</template>

<script setup lang="ts">
import { defineEmits, onMounted, ref, watch } from 'vue'
import { useBookMetaStore } from '@/stores/bookMetaStore'
import type { Tag, Book } from '@/types/book'
import { editBookTagsApi } from '@/api/book'
import { Toast } from '@/utils/toast'

// 使用书籍元数据 store
const bookMetaStore = useBookMetaStore()

interface Props {
  visible: boolean
  bookMeta: { id: string | number; tags: Tag[] }
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
  (e: 'on-close'): void
  (e: 'on-success'): void
}>()

// 内部 visibe
const insideVisible = ref(props.visible)

// 已选中
const liTagsIsChosing = ref<Tag[]>([])

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 如果是打开，则从父组件值，初始化 已选中 tags
      liTagsIsChosing.value = [...props.bookMeta.tags]
    }

    insideVisible.value = newVal
  },
)

/**
 * 切换-选中标签
 */
const toggleEditTagChose = (tag: Tag) => {
  if (liTagsIsChosing.value.some((tmp: Tag) => tmp.id === tag.id)) {
    // 已存在，则取消
    liTagsIsChosing.value = liTagsIsChosing.value.filter((tmp) => tmp.id !== tag.id)
  } else {
    // 不存在，则添加
    liTagsIsChosing.value.push(tag)
  }
}

/**
 * 内部 关闭事件
 */
const close = () => {
  insideVisible.value = false // 内部关闭
  emit('on-close') // 通知外部关闭
}

const submit = async () => {
  try {
    await editBookTagsApi(
      props.bookMeta.id,
      liTagsIsChosing.value.map((item) => item.id),
    )
    Toast.success('修改成功')

    // 关闭
    close()

    // 刷新
    emit('on-success')
  } catch (error) {
    Toast.error('修改失败', 3000) // 3秒后自动关闭
    return
  }
}

onMounted(() => {
  bookMetaStore.fetchTags()
})
</script>

<style lang="scss" scoped>
.edit-tags--show-block {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;

  @media (max-width: $media-width) {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: $gap;
  }

  .single-tag {
    // @include flex-center;
    padding: 8px 12px;
    background: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media (max-width: $media-width) {
      padding: 4px;
      font-size: 11px;
    }

    &:hover {
      background-color: #e8f3ff;
      color: #4a90e2;
    }

    &.active {
      background-color: #4a90e2;
      color: white;
    }
  }

  .single-tag.active {
    background-color: #2d9635;
  }
}
</style>
