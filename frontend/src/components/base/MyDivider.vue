<template>
  <!-- 横向分隔符（默认） -->
  <div
    v-if="direction === 'horizontal'"
    class="my-divider my-divider--horizontal"
    :class="{ 'is-dashed': dashed }"
    role="separator"
  >
    <template v-if="hasDefaultSlot">
      <span class="my-divider__line"></span>
      <span class="my-divider__content"><slot /></span>
      <span class="my-divider__line"></span>
    </template>
    <template v-else>
      <span class="my-divider__line"></span>
    </template>
  </div>

  <!-- 纵向分隔符（简单高效） -->
  <div
    v-else
    class="my-divider my-divider--vertical"
    :class="{ 'is-dashed': dashed }"
    role="separator"
  />
</template>

<script setup lang="ts">
import { computed, useSlots } from 'vue'

type Direction = 'horizontal' | 'vertical'

interface Props {
  direction?: Direction
  dashed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'horizontal',
  dashed: false,
})

const slots = useSlots()
const hasDefaultSlot = computed(() => !!slots.default)
</script>

<style scoped>
.my-divider {
  --line-color: #e4e7ed;
  --text-color: #909399;
}

/* 横向 */
.my-divider--horizontal {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 16px 0;
}

.my-divider__line {
  height: 0;
  border-top: 1px solid var(--line-color);
  flex: 1;
}

.my-divider.is-dashed .my-divider__line {
  border-top-style: dashed;
}

.my-divider__content {
  padding: 0 12px;
  color: var(--text-color);
  font-size: 12px;
  white-space: nowrap;
}

/* 纵向（用于紧凑布局的简单分隔） */
.my-divider--vertical {
  display: inline-block;
  height: 1em;
  border-left: 1px solid var(--line-color);
  margin: 0 8px;
  vertical-align: middle;
}

.my-divider--vertical.is-dashed {
  border-left-style: dashed;
}
</style>
