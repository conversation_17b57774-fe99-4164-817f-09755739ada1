<template>
  <div
    ref="containerRef"
    class="my-select-wrapper"
    :class="{ 'is-open': isOpen, 'is-disabled': disabled, 'can-clear': showClearButton }"
  >
    <div
      class="my-select-selection my-select-selection--normal"
      :class="{
        'my-select-selection--placeholder': !hasValue,
        'my-select-selection--with-prefix': $slots.prefix,
      }"
      @click="toggleDropdown"
    >
      <div v-if="$slots.prefix" class="my-select__prefix">
        <slot name="prefix"></slot>
      </div>
      <span class="my-select-selection__label">{{ displayLabel }}</span>

      <button
        v-if="showClearButton"
        type="button"
        class="my-select__clear my-select__clear--normal"
        @click.stop="handleClear"
        @mousedown.prevent
      >
        <font-awesome-icon icon="fa-solid fa-circle-xmark" />
      </button>

      <span class="my-select-selection__arrow" :class="{ 'is-rotated': isOpen }">
        <font-awesome-icon icon="fa-solid fa-angle-down" />
      </span>
    </div>

    <ul v-if="isOpen" class="my-select-dropdown">
      <li
        v-for="option in options"
        :key="getOptionKey(option)"
        class="my-select-option"
        :class="{
          'is-selected': isSelected(option),
          'is-disabled': !!option.disabled,
        }"
        @click="handleSelect(option)"
      >
        <span class="my-select-option__label">{{ option.label }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'

type OptionValue = string | number | boolean

interface SelectOption {
  label: string
  value: OptionValue
  disabled?: boolean
}

interface Props {
  modelValue?: OptionValue | null
  options: SelectOption[]
  placeholder?: string
  disabled?: boolean
  clear?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: OptionValue | null): void
  (e: 'change', value: OptionValue | null): void
  (e: 'clear'): void
  (e: 'visible-change', visible: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  options: () => [],
  placeholder: '请选择',
  disabled: false,
  clear: false,
})

const emit = defineEmits<Emits>()

const containerRef = ref<HTMLElement | null>(null)
const isOpen = ref(false)

const selectedOption = computed(() => {
  return props.options.find((opt) => opt.value === props.modelValue)
})

const hasValue = computed(() => selectedOption.value !== undefined)

const displayLabel = computed(() => {
  return hasValue.value ? selectedOption.value!.label : props.placeholder
})

const showClearButton = computed(() => props.clear && !props.disabled && hasValue.value)

const toggleDropdown = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  if (!isOpen.value) return
  isOpen.value = false
}

const handleSelect = (option: SelectOption) => {
  if (option.disabled) return
  emit('update:modelValue', option.value)
  emit('change', option.value)
  closeDropdown()
}

const handleClear = () => {
  emit('update:modelValue', null)
  emit('change', null)
  emit('clear')
}

const onClickOutside = (e: MouseEvent) => {
  const el = containerRef.value
  if (!el) return
  if (e.target instanceof Node && !el.contains(e.target)) {
    closeDropdown()
  }
}

watch(isOpen, (visible) => emit('visible-change', visible))

onMounted(() => {
  document.addEventListener('click', onClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', onClickOutside)
})

const getOptionKey = (option: SelectOption) => `${String(option.value)}-${option.label}`
const isSelected = (option: SelectOption) => option.value === props.modelValue
</script>

<style scoped lang="scss">
$base-height: 32px;

.my-select-wrapper {
  position: relative;
  /* width: 100%; */
  height: $base-height;
  width: 200px;
}

.my-select-selection {
  height: $base-height;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  /* line-height: 1.5; */
  color: #333;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.my-select-selection--normal {
  padding: 6px 28px 6px 12px;
  font-size: 13px;
}

.my-select-selection--with-prefix {
  padding-left: 28px;
}

.my-select-selection:hover {
  border-color: #40a9ff;
}

.my-select-selection:focus-within,
.is-open .my-select-selection {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.my-select-selection--placeholder {
  color: #bfbfbf;
}

.is-disabled .my-select-selection {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
}

.my-select__prefix {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  display: flex;
  align-items: center;
  height: 100%;
  color: #848484;
  pointer-events: none;
  left: 10px;
}

.my-select-selection__label {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.my-select-selection__arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #bfbfbf;
  transition: transform 0.2s ease;
}

.my-select-selection__arrow.is-rotated {
  transform: translateY(-50%) rotate(180deg);
}

.my-select__clear {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #bfbfbf;
  opacity: 0;
  visibility: hidden;
}

.my-select__clear--normal {
  right: 4px;
  padding: 3px;
  font-size: 15px;
}

.my-select-wrapper.can-clear:hover .my-select__clear {
  opacity: 1;
  visibility: visible;
}

.my-select-wrapper.can-clear:hover .my-select-selection__arrow {
  opacity: 0;
  visibility: hidden;
}

.my-select__clear:hover {
  background-color: #f0f0f0;
  color: #666;
}

.my-select__clear:active {
  background-color: #e6e6e6;
  color: #333;
}

.my-select-dropdown {
  position: absolute;
  z-index: 1000;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  max-height: 220px;
  overflow: auto;
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.08),
    0 3px 6px rgba(0, 0, 0, 0.06);
  padding: 6px 0;
}

.my-select-option {
  padding: 6px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.my-select-option:hover {
  background: #f5f7fa;
}

.my-select-option.is-selected {
  color: #1890ff;
  background: #e6f4ff;
}

.my-select-option.is-disabled {
  color: #bfbfbf;
  cursor: not-allowed;
}

.my-select-option.is-disabled:hover {
  background: transparent;
}
</style>
