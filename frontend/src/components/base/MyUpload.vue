<template>
  <div
    class="simple-upload"
    :class="{ 'is-dragover': isDragOver, 'has-file': selectedFile }"
    :style="{ width: width + 'px', height: height + 'px' }"
    @click="handleClick"
    @dragover.prevent="handleDragOver"
    @dragleave.prevent="handleDragLeave"
    @drop.prevent="handleDrop"
  >
    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInput"
      type="file"
      :accept="acceptTypes"
      @change="handleFileSelect"
      style="display: none"
    />
    
    <!-- 上传区域内容 -->
    <div class="upload-content">
      <div v-if="!selectedFile" class="upload-placeholder">
        <div class="upload-icon">📁</div>
        <div class="upload-text">
          <p>点击或拖拽文件到此处上传</p>
          <p class="upload-hint" v-if="acceptTypesText">
            支持格式：{{ acceptTypesText }}
          </p>
        </div>
      </div>
      
      <div v-else class="file-info">
        <div class="file-icon">📄</div>
        <div class="file-details">
          <p class="file-name">{{ selectedFile.name }}</p>
          <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
        </div>
        <button class="clear-btn" @click.stop="clearFile">✕</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineEmits, defineExpose, ref } from 'vue'

// Props
const props = defineProps({
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 300
  },
  accept: {
    type: [String, Array],
    default: ''
  },
  maxSize: {
    type: Number,
    default: 0 // 0 表示不限制大小，单位：字节
  }
})

// Emits
const emit = defineEmits(['file-selected', 'file-cleared', 'error'])

// 响应式数据
const fileInput = ref(null)
const selectedFile = ref(null)
const isDragOver = ref(false)

// 计算属性
const acceptTypes = computed(() => {
  if (!props.accept) return ''
  if (Array.isArray(props.accept)) {
    return props.accept.map(type => {
      const lowerType = type.toLowerCase()

      // 以点开头的扩展名直接返回
      if (lowerType.startsWith('.')) return lowerType

      // 通用类型
      if (lowerType === 'image') return 'image/*'
      if (lowerType === 'video') return 'video/*'
      if (lowerType === 'audio') return 'audio/*'

      // 压缩文件类型的 MIME 类型
      if (lowerType === 'zip') return '.zip,application/zip,application/x-zip-compressed'
      if (lowerType === 'rar') return '.rar,application/x-rar-compressed'
      if (lowerType === '7z') return '.7z,application/x-7z-compressed'

      // 其他扩展名
      return `.${lowerType}`
    }).join(',')
  }
  return props.accept
})

const acceptTypesText = computed(() => {
  if (!props.accept) return ''
  if (Array.isArray(props.accept)) {
    return props.accept.join(', ')
  }
  return props.accept
})

// 方法
const handleClick = () => {
  fileInput.value?.click()
}

const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (e) => {
  isDragOver.value = false
  const files = e.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

const handleFileSelect = (e) => {
  const files = e.target.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

const processFile = (file) => {
  // 验证文件类型
  if (props.accept && !validateFileType(file)) {
    emit('error', {
      type: 'invalid-type',
      message: `不支持的文件类型：${file.name}`
    })
    return
  }
  
  // 验证文件大小
  if (props.maxSize > 0 && file.size > props.maxSize) {
    emit('error', {
      type: 'size-exceeded',
      message: `文件大小超出限制：${formatFileSize(file.size)}`
    })
    return
  }
  
  selectedFile.value = file
  emit('file-selected', file)
}

const validateFileType = (file) => {
  if (!props.accept) return true

  const fileName = file.name.toLowerCase()
  const fileType = file.type.toLowerCase()

  if (Array.isArray(props.accept)) {
    return props.accept.some(type => {
      const lowerType = type.toLowerCase()

      // 通用类型
      if (lowerType === 'image') return fileType.startsWith('image/')
      if (lowerType === 'video') return fileType.startsWith('video/')
      if (lowerType === 'audio') return fileType.startsWith('audio/')

      // 特殊处理压缩文件类型
      if (lowerType === 'zip') {
        return fileName.endsWith('.zip') || fileType === 'application/zip' || fileType === 'application/x-zip-compressed'
      }
      if (lowerType === 'rar') {
        return fileName.endsWith('.rar') || fileType === 'application/x-rar-compressed'
      }
      if (lowerType === '7z') {
        return fileName.endsWith('.7z') || fileType === 'application/x-7z-compressed'
      }

      // 以点开头的扩展名
      if (lowerType.startsWith('.')) return fileName.endsWith(lowerType)

      // 普通扩展名
      return fileName.endsWith(`.${lowerType}`)
    })
  }

  return true
}

const clearFile = () => {
  selectedFile.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  emit('file-cleared')
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  clearFile,
  getFile: () => selectedFile.value
})
</script>

<style scoped>
.simple-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  position: relative;
  overflow: hidden;
}

.simple-upload:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.simple-upload.is-dragover {
  border-color: #409eff;
  background-color: #e6f7ff;
  transform: scale(1.02);
}

.simple-upload.has-file {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.upload-content {
  text-align: center;
  padding: 20px;
  width: 100%;
}

.upload-placeholder {
  color: #666;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  font-size: 14px;
}

.upload-hint {
  color: #999;
  font-size: 12px !important;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 20px;
}

.file-icon {
  font-size: 32px;
  margin-right: 12px;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #333;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.clear-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-btn:hover {
  background: #ff3742;
  transform: scale(1.1);
}
</style>
