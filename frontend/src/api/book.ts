import http, { httpBinary } from '@/utils/http';


// 书本-下载（二进制）
export const downloadBookApi = async (book_id: string) => {
    // 返回的是 AxiosResponse<Blob>
    return await httpBinary.get(`/book/${book_id}/download/`)
}

// 获取 genres 列表
export const getGenresListApi = async (): Promise<any> => {
    return await http.get('/genres/');
}

// 获取 tags 列表
export const getTagsListApi = async (): Promise<any> => {
    return await http.get('/tags/');
}

// 获取-专题-列表
export const getSubjectsListApi = async (params: any): Promise<any> => {
    return await http.get('/subjects/', { params });
}

// 获取 book 列表
export const getBookListApi = async (params: any): Promise<any> => {
    return await http.get('/books/', { params });
}

// 获取 book 页面 列表
export const getBookPagesListApi = async (bookId: string | number, params: any): Promise<any> => {
    return await http.get(`/book/${bookId}/pages/list`, { params });
}

// 切换收藏
export const toggleCollectApi = async (bookId: string | number): Promise<any> => {
    return await http.post(`/book/${bookId}/collect/`);
}

// 修改-类型
export const editGenreApi = async (bookId: string | number, genre: string): Promise<any> => {
    return await http.post(`/book/${bookId}/genre/`, { genre });
}

// 修改-书本-标签
export const editBookTagsApi = async (bookId: string | number, li_tag_id: (string | number)[]): Promise<any> => {
    return await http.post(`/book/${bookId}/tags/`, { li_tag_id });
}

// 修改-书本-专题
export const editBookSubjectsApi = async (bookId: string | number, li_subject_id: (string | number)[]): Promise<any> => {
    return await http.post(`/book/${bookId}/subjects/`, { li_subject_id });
}

// 修改-书本-标题
export const editBookTitleApi = async (bookId: string | number, title: string): Promise<any> => {
    return await http.post(`/book/${bookId}/title/`, { title });
}

// 添加-标签
export const addTagApi = async (value: string): Promise<any> => {
    return await http.post(`/tag/add/`, { value });
}

// 获取-书本-详情
export const getBookDetailsApi = async (book_id: string): Promise<any> => {
    return await http.get(`/book/${book_id}/`)
}

// 书本-删除
export const delBookApi = async (book_id: string): Promise<any> => {
    return await http.get(`/book/${book_id}/del/`)
}

// 书本-页面-删除
export const delBookPageApi = async (book_id: string | number, page_id: string | number): Promise<any> => {
    return await http.get(`/book/${book_id}/${page_id}/del/`)
}

// 书本-页面-排序
export const bookPagesSortApi = async (book_id: string | number, li_page_id: (string | number)[]): Promise<any> => {
    return await http.post(`book/${book_id}/pages/sort/`, { li_page_id })
}


// 书本-合并
export const combineBooksApi = async (li_book_id: string[]): Promise<any> => {
    return await http.post('books/combine/', { li_book_id })
}