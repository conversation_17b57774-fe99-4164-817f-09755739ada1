# Docker Compose 配置文件
# 用于编排前后端服务
version: '3.8'

services:
  # ==================== redis 配置 ====================
  redis_docker_container:
    container_name: my_steward_pro__redis
    image: redis:alpine
    ports:
      # 宿主端口 12445 : 容器内端口 6379
      - "12445:6379"

  # ==================== 前端服务配置 ====================
  frontend:
    container_name: my_steward_pro__frontend
    # 构建配置
    build:
      context: ./frontend          # 构建上下文：指定前端代码目录
      dockerfile: Dockerfile       # 使用的 Dockerfile 文件名
    volumes:
      # 将 frontend 构建中编译的 /app/dist，挂载到 命名卷
      - frontend_dist:/app/dist
    # 不需要暴露端口，nginx 代理

  # ==================== 后端服务配置 ====================
  backend:
    container_name: my_steward_pro__backend
    # 构建配置
    build:
      context: ./backend           # 构建上下文：指定后端代码目录
      dockerfile: Dockerfile       # 使用的 Dockerfile 文件名
    # 端口映射
    ports:
      - "12345:12345"             # 将主机的12345端口映射到容器的12345端口
    # 环境变量设置
    environment:
      - DEBUG=False               # 关闭调试模式（生产环境）
      - DJANGO_SETTINGS_MODULE=main.settings  # 指定Django设置模块
      - DOCKER_ENV=true          # 标识运行在Docker环境中
    # 数据卷挂载：将本地目录挂载到容器内
    volumes:
      - backend_static:/app/_static
      - /Users/<USER>/Documents/my_steward/chang_path_demo/media:/app/media
      - /Users/<USER>/Documents/my_steward/chang_path_demo/db.sqlite3:/app/db.sqlite3
      # 挂载 日志，便于外部查看
      - ./backend/logs:/app/logs
    depends_on:
      # 确保 redis 先启动
      - redis_docker_container

  nginx:
    container_name: my_steward_pro__nginx
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - /Users/<USER>/Documents/my_steward/chang_path_demo/media:/res/media:ro
      - frontend_dist:/res/dist:ro
      - backend_static:/res/static:ro
    depends_on:
      - frontend
      - backend 
      
volumes:  # 命名卷（中转站），多个容器挂载同一个 volume，共享同一份数据
  frontend_dist:
  backend_static: